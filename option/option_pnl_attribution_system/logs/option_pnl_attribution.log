2025-05-27 01:07:40 | INFO     | __main__:__init__:46 - Option PnL Attribution System initialized
2025-05-27 01:07:40 | INFO     | src.data_processor:create_sample_data:60 - Creating sample data files
2025-05-27 01:07:40 | INFO     | src.data_processor:_create_sample_positions:127 - Created 160 sample option contracts in data/input/positions.xlsx
2025-05-27 01:07:40 | INFO     | src.data_processor:_create_sample_market_data:174 - Created market data for 4 underlyings in data/input/market_data.xlsx
2025-05-27 01:07:40 | INFO     | src.data_processor:create_sample_data:71 - Sample data files created successfully
2025-05-27 01:07:40 | INFO     | __main__:create_sample_data:280 - Sample data created successfully
2025-05-27 01:07:52 | INFO     | __main__:__init__:46 - Option PnL Attribution System initialized
2025-05-27 01:07:52 | ERROR    | src.database_exporter:connect:75 - Failed to connect to database: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost' ([<PERSON>rr<PERSON> 61] Connection refused)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-27 01:08:02 | INFO     | __main__:__init__:46 - Option PnL Attribution System initialized
2025-05-27 01:08:02 | INFO     | src.data_processor:load_positions_data:186 - Loaded 160 position records
2025-05-27 01:08:02 | INFO     | src.data_processor:load_positions_data:186 - Loaded 160 position records
2025-05-27 01:08:02 | INFO     | __main__:run_full_analysis:101 - Starting full PnL attribution analysis
2025-05-27 01:08:02 | INFO     | __main__:run_full_analysis:105 - Step 1: Loading and processing data
2025-05-27 01:08:02 | INFO     | src.data_processor:load_positions_data:186 - Loaded 160 position records
2025-05-27 01:08:02 | INFO     | src.data_processor:load_market_data:202 - Loaded 120 market data records
2025-05-27 01:08:02 | INFO     | src.data_processor:merge_data:254 - Merged data: 160 records
2025-05-27 01:08:02 | INFO     | __main__:run_full_analysis:119 - Step 2: Calculating Greeks
2025-05-27 01:08:02 | INFO     | __main__:run_full_analysis:123 - Step 3: Calculating PnL attribution
2025-05-27 01:08:02 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-05-27 01:08:02 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-05-27 01:08:02 | INFO     | __main__:run_full_analysis:129 - Step 4: Generating summary reports
2025-05-27 01:08:02 | INFO     | __main__:run_full_analysis:133 - Step 5: Preparing database export data
2025-05-27 01:08:02 | INFO     | src.data_processor:prepare_database_export_data:308 - Prepared database export data: 160 records, 56 columns
2025-05-27 01:08:02 | INFO     | __main__:run_full_analysis:139 - Step 6: Saving results to Excel
2025-05-27 01:08:02 | INFO     | src.data_processor:save_to_excel:329 - Data saved to data/output/option_pnl_attribution_detail_20250527_010802.xlsx
2025-05-27 01:08:02 | INFO     | src.data_processor:save_to_excel:329 - Data saved to data/output/option_pnl_attribution_summary_20250527_010802.xlsx
2025-05-27 01:08:03 | INFO     | src.data_processor:save_to_excel:329 - Data saved to data/output/database_export.xlsx
2025-05-27 01:08:03 | ERROR    | __main__:run_full_analysis:165 - PnL attribution analysis failed: name 'np' is not defined
2025-05-27 01:08:40 | INFO     | __main__:__init__:47 - Option PnL Attribution System initialized
2025-05-27 01:08:40 | INFO     | src.data_processor:load_positions_data:186 - Loaded 160 position records
2025-05-27 01:08:40 | INFO     | src.data_processor:load_positions_data:186 - Loaded 160 position records
2025-05-27 01:08:40 | INFO     | __main__:run_full_analysis:102 - Starting full PnL attribution analysis
2025-05-27 01:08:40 | INFO     | __main__:run_full_analysis:106 - Step 1: Loading and processing data
2025-05-27 01:08:40 | INFO     | src.data_processor:load_positions_data:186 - Loaded 160 position records
2025-05-27 01:08:40 | INFO     | src.data_processor:load_market_data:202 - Loaded 120 market data records
2025-05-27 01:08:40 | INFO     | src.data_processor:merge_data:254 - Merged data: 160 records
2025-05-27 01:08:40 | INFO     | __main__:run_full_analysis:120 - Step 2: Calculating Greeks
2025-05-27 01:08:40 | INFO     | __main__:run_full_analysis:124 - Step 3: Calculating PnL attribution
2025-05-27 01:08:40 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-05-27 01:08:40 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-05-27 01:08:40 | INFO     | __main__:run_full_analysis:130 - Step 4: Generating summary reports
2025-05-27 01:08:40 | INFO     | __main__:run_full_analysis:134 - Step 5: Preparing database export data
2025-05-27 01:08:40 | INFO     | src.data_processor:prepare_database_export_data:308 - Prepared database export data: 160 records, 56 columns
2025-05-27 01:08:40 | INFO     | __main__:run_full_analysis:140 - Step 6: Saving results to Excel
2025-05-27 01:08:40 | INFO     | src.data_processor:save_to_excel:329 - Data saved to data/output/option_pnl_attribution_detail_20250527_010840.xlsx
2025-05-27 01:08:40 | INFO     | src.data_processor:save_to_excel:329 - Data saved to data/output/option_pnl_attribution_summary_20250527_010840.xlsx
2025-05-27 01:08:40 | INFO     | src.data_processor:save_to_excel:329 - Data saved to data/output/database_export.xlsx
2025-05-27 01:08:40 | INFO     | __main__:run_full_analysis:162 - Full PnL attribution analysis completed successfully
2025-05-27 01:09:56 | INFO     | main:__init__:47 - Option PnL Attribution System initialized
2025-05-27 01:09:56 | INFO     | src.data_processor:__init__:32 - Data processor initialized
2025-05-27 01:09:56 | INFO     | src.pnl_attribution:__init__:37 - PnL Attribution Calculator initialized
2025-05-27 01:09:56 | INFO     | src.database_exporter:__init__:31 - Database exporter initialized
2025-05-27 01:09:56 | INFO     | main:__init__:47 - Option PnL Attribution System initialized
2025-05-27 01:09:56 | INFO     | src.data_processor:create_sample_data:60 - Creating sample data files
2025-05-27 01:09:56 | INFO     | src.data_processor:_create_sample_positions:127 - Created 160 sample option contracts in data/input/positions.xlsx
2025-05-27 01:09:56 | INFO     | src.data_processor:_create_sample_market_data:174 - Created market data for 4 underlyings in data/input/market_data.xlsx
2025-05-27 01:09:56 | INFO     | src.data_processor:create_sample_data:71 - Sample data files created successfully
2025-05-27 01:09:56 | INFO     | main:create_sample_data:281 - Sample data created successfully
2025-05-27 01:09:56 | INFO     | src.data_processor:__init__:32 - Data processor initialized
2025-05-27 01:09:56 | INFO     | src.pnl_attribution:__init__:37 - PnL Attribution Calculator initialized
2025-05-27 01:09:56 | INFO     | src.database_exporter:__init__:31 - Database exporter initialized
2025-05-27 01:09:56 | INFO     | main:__init__:47 - Option PnL Attribution System initialized
2025-05-27 01:09:56 | INFO     | src.data_processor:__init__:32 - Data processor initialized
2025-05-27 01:09:56 | INFO     | src.pnl_attribution:__init__:37 - PnL Attribution Calculator initialized
2025-05-27 01:09:56 | INFO     | src.database_exporter:__init__:31 - Database exporter initialized
2025-05-27 01:09:56 | INFO     | main:__init__:47 - Option PnL Attribution System initialized
2025-05-27 01:09:56 | INFO     | src.data_processor:__init__:32 - Data processor initialized
2025-05-27 01:09:56 | INFO     | src.pnl_attribution:__init__:37 - PnL Attribution Calculator initialized
2025-05-27 01:09:56 | INFO     | src.database_exporter:__init__:31 - Database exporter initialized
2025-05-27 01:09:56 | INFO     | main:__init__:47 - Option PnL Attribution System initialized
2025-05-27 01:09:56 | INFO     | src.data_processor:__init__:32 - Data processor initialized
2025-05-27 01:09:56 | INFO     | src.pnl_attribution:__init__:37 - PnL Attribution Calculator initialized
2025-05-27 01:09:56 | INFO     | src.database_exporter:__init__:31 - Database exporter initialized
2025-05-27 01:09:56 | INFO     | main:__init__:47 - Option PnL Attribution System initialized
2025-05-27 01:09:56 | ERROR    | src.database_exporter:connect:75 - Failed to connect to database: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost' ([Errno 61] Connection refused)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-27 01:09:56 | INFO     | src.data_processor:__init__:32 - Data processor initialized
2025-05-27 01:09:56 | INFO     | src.data_processor:__init__:32 - Data processor initialized
2025-05-27 01:09:56 | INFO     | src.data_processor:create_sample_data:60 - Creating sample data files
2025-05-27 01:09:56 | INFO     | src.data_processor:_create_sample_positions:127 - Created 160 sample option contracts in data/input/positions.xlsx
2025-05-27 01:09:56 | INFO     | src.data_processor:_create_sample_market_data:174 - Created market data for 4 underlyings in data/input/market_data.xlsx
2025-05-27 01:09:56 | INFO     | src.data_processor:create_sample_data:71 - Sample data files created successfully
2025-05-27 01:09:56 | INFO     | src.pnl_attribution:__init__:37 - PnL Attribution Calculator initialized
2025-05-27 01:09:56 | INFO     | src.pnl_attribution:__init__:37 - PnL Attribution Calculator initialized
2025-05-27 01:09:56 | INFO     | src.pnl_attribution:__init__:37 - PnL Attribution Calculator initialized
2025-05-27 01:09:56 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-05-27 01:09:56 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-05-27 01:12:00 | INFO     | main:__init__:47 - Option PnL Attribution System initialized
2025-05-27 01:12:00 | INFO     | src.data_processor:create_sample_data:60 - Creating sample data files
2025-05-27 01:12:00 | INFO     | src.data_processor:_create_sample_positions:127 - Created 160 sample option contracts in data/input/positions.xlsx
2025-05-27 01:12:00 | INFO     | src.data_processor:_create_sample_market_data:174 - Created market data for 4 underlyings in data/input/market_data.xlsx
2025-05-27 01:12:00 | INFO     | src.data_processor:create_sample_data:71 - Sample data files created successfully
2025-05-27 01:12:00 | INFO     | main:create_sample_data:281 - Sample data created successfully
2025-05-27 01:12:00 | ERROR    | src.database_exporter:connect:75 - Failed to connect to database: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost' ([Errno 61] Connection refused)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-27 01:12:00 | INFO     | main:run_full_analysis:102 - Starting full PnL attribution analysis
2025-05-27 01:12:00 | INFO     | main:run_full_analysis:106 - Step 1: Loading and processing data
2025-05-27 01:12:00 | INFO     | src.data_processor:load_positions_data:186 - Loaded 160 position records
2025-05-27 01:12:00 | INFO     | src.data_processor:load_market_data:202 - Loaded 120 market data records
2025-05-27 01:12:00 | INFO     | src.data_processor:merge_data:254 - Merged data: 160 records
2025-05-27 01:12:00 | INFO     | main:run_full_analysis:120 - Step 2: Calculating Greeks
2025-05-27 01:12:00 | INFO     | main:run_full_analysis:124 - Step 3: Calculating PnL attribution
2025-05-27 01:12:00 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-05-27 01:12:00 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-05-27 01:12:00 | INFO     | main:run_full_analysis:130 - Step 4: Generating summary reports
2025-05-27 01:12:00 | INFO     | main:run_full_analysis:134 - Step 5: Preparing database export data
2025-05-27 01:12:00 | INFO     | src.data_processor:prepare_database_export_data:308 - Prepared database export data: 160 records, 56 columns
2025-05-27 01:12:00 | INFO     | main:run_full_analysis:140 - Step 6: Saving results to Excel
2025-05-27 01:12:01 | INFO     | src.data_processor:save_to_excel:329 - Data saved to data/output/option_pnl_attribution_detail_20250527_011200.xlsx
2025-05-27 01:12:01 | INFO     | src.data_processor:save_to_excel:329 - Data saved to data/output/option_pnl_attribution_summary_20250527_011200.xlsx
2025-05-27 01:12:01 | INFO     | src.data_processor:save_to_excel:329 - Data saved to data/output/database_export.xlsx
2025-05-27 01:12:01 | INFO     | main:run_full_analysis:162 - Full PnL attribution analysis completed successfully
2025-05-27 01:12:01 | INFO     | src.data_processor:__init__:32 - Data processor initialized
2025-05-27 01:12:01 | INFO     | src.pnl_attribution:__init__:37 - PnL Attribution Calculator initialized
2025-05-27 01:12:01 | INFO     | src.database_exporter:__init__:31 - Database exporter initialized
2025-05-27 01:12:01 | INFO     | main:__init__:47 - Option PnL Attribution System initialized
2025-05-27 01:12:01 | INFO     | main:run_full_analysis:102 - Starting full PnL attribution analysis
2025-05-27 01:12:01 | INFO     | main:run_full_analysis:106 - Step 1: Loading and processing data
2025-05-27 01:12:01 | INFO     | src.data_processor:load_positions_data:186 - Loaded 160 position records
2025-05-27 01:12:01 | INFO     | src.data_processor:load_market_data:202 - Loaded 120 market data records
2025-05-27 01:12:01 | INFO     | src.data_processor:merge_data:254 - Merged data: 160 records
2025-05-27 01:12:01 | INFO     | main:run_full_analysis:120 - Step 2: Calculating Greeks
2025-05-27 01:12:01 | INFO     | main:run_full_analysis:124 - Step 3: Calculating PnL attribution
2025-05-27 01:12:01 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-05-27 01:12:01 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-05-27 01:12:01 | INFO     | main:run_full_analysis:130 - Step 4: Generating summary reports
2025-05-27 01:12:01 | INFO     | main:run_full_analysis:134 - Step 5: Preparing database export data
2025-05-27 01:12:01 | INFO     | src.data_processor:prepare_database_export_data:308 - Prepared database export data: 160 records, 56 columns
2025-05-27 01:12:01 | INFO     | main:run_full_analysis:140 - Step 6: Saving results to Excel
2025-05-27 01:12:01 | INFO     | src.data_processor:save_to_excel:329 - Data saved to data/output/option_pnl_attribution_detail_20250527_011201.xlsx
2025-05-27 01:12:01 | INFO     | src.data_processor:save_to_excel:329 - Data saved to data/output/option_pnl_attribution_summary_20250527_011201.xlsx
2025-05-27 01:12:01 | INFO     | src.data_processor:save_to_excel:329 - Data saved to data/output/database_export.xlsx
2025-05-27 01:12:01 | INFO     | main:run_full_analysis:162 - Full PnL attribution analysis completed successfully
2025-05-27 01:12:01 | INFO     | main:run_full_analysis:102 - Starting full PnL attribution analysis
2025-05-27 01:12:01 | INFO     | main:run_full_analysis:106 - Step 1: Loading and processing data
2025-05-27 01:12:01 | INFO     | src.data_processor:load_positions_data:186 - Loaded 160 position records
2025-05-27 01:12:01 | INFO     | src.data_processor:load_market_data:202 - Loaded 120 market data records
2025-05-27 01:12:01 | INFO     | src.data_processor:merge_data:254 - Merged data: 160 records
2025-05-27 01:12:01 | INFO     | main:run_full_analysis:120 - Step 2: Calculating Greeks
2025-05-27 01:12:01 | INFO     | main:run_full_analysis:124 - Step 3: Calculating PnL attribution
2025-05-27 01:12:01 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-05-27 01:12:01 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-05-27 01:12:01 | INFO     | main:run_full_analysis:130 - Step 4: Generating summary reports
2025-05-27 01:12:01 | INFO     | main:run_full_analysis:134 - Step 5: Preparing database export data
2025-05-27 01:12:01 | INFO     | src.data_processor:prepare_database_export_data:308 - Prepared database export data: 160 records, 56 columns
2025-05-27 01:12:01 | INFO     | main:run_full_analysis:140 - Step 6: Saving results to Excel
2025-05-27 01:12:01 | INFO     | src.data_processor:save_to_excel:329 - Data saved to data/output/option_pnl_attribution_detail_20250527_011201.xlsx
2025-05-27 01:12:01 | INFO     | src.data_processor:save_to_excel:329 - Data saved to data/output/option_pnl_attribution_summary_20250527_011201.xlsx
2025-05-27 01:12:01 | INFO     | src.data_processor:save_to_excel:329 - Data saved to data/output/database_export.xlsx
2025-05-27 01:12:01 | INFO     | main:run_full_analysis:162 - Full PnL attribution analysis completed successfully
2025-05-27 01:12:01 | INFO     | main:run_full_analysis:102 - Starting full PnL attribution analysis
2025-05-27 01:12:01 | INFO     | main:run_full_analysis:106 - Step 1: Loading and processing data
2025-05-27 01:12:01 | INFO     | src.data_processor:load_positions_data:186 - Loaded 160 position records
2025-05-27 01:12:01 | INFO     | src.data_processor:load_market_data:202 - Loaded 120 market data records
2025-05-27 01:12:01 | INFO     | src.data_processor:merge_data:254 - Merged data: 160 records
2025-05-27 01:12:01 | INFO     | main:run_full_analysis:120 - Step 2: Calculating Greeks
2025-05-27 01:12:01 | INFO     | main:run_full_analysis:124 - Step 3: Calculating PnL attribution
2025-05-27 01:12:01 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-05-27 01:12:01 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-05-27 01:12:01 | INFO     | main:run_full_analysis:130 - Step 4: Generating summary reports
2025-05-27 01:12:01 | INFO     | main:run_full_analysis:134 - Step 5: Preparing database export data
2025-05-27 01:12:01 | INFO     | src.data_processor:prepare_database_export_data:308 - Prepared database export data: 160 records, 56 columns
2025-05-27 01:12:01 | INFO     | main:run_full_analysis:140 - Step 6: Saving results to Excel
2025-05-27 01:12:01 | INFO     | src.data_processor:save_to_excel:329 - Data saved to data/output/option_pnl_attribution_detail_20250527_011201.xlsx
2025-05-27 01:12:01 | INFO     | src.data_processor:save_to_excel:329 - Data saved to data/output/option_pnl_attribution_summary_20250527_011201.xlsx
2025-05-27 01:12:01 | INFO     | src.data_processor:save_to_excel:329 - Data saved to data/output/database_export.xlsx
2025-05-27 01:12:01 | INFO     | main:run_full_analysis:162 - Full PnL attribution analysis completed successfully
2025-05-28 00:43:14 | INFO     | main:__init__:49 - Option PnL Attribution System initialized
2025-05-28 00:43:14 | INFO     | src.data_processor:create_sample_data:61 - Creating sample data files
2025-05-28 00:43:14 | INFO     | src.data_processor:_create_sample_consolidated_data:126 - Created 160 sample option contracts in data/input/consolidated_option_data.xlsx
2025-05-28 00:43:14 | INFO     | src.data_processor:create_sample_data:69 - Sample data files created successfully
2025-05-28 00:43:14 | INFO     | main:create_sample_data:305 - Sample data created successfully
2025-05-28 00:43:14 | INFO     | main:run_full_analysis:106 - Starting full PnL attribution analysis
2025-05-28 00:43:14 | INFO     | main:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-05-28 00:43:14 | INFO     | src.data_processor:load_consolidated_data:138 - Loaded 160 consolidated option records
2025-05-28 00:43:14 | INFO     | src.data_processor:merge_data:189 - Processed consolidated data: 160 records
2025-05-28 00:43:14 | INFO     | main:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-05-28 00:43:14 | INFO     | main:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-05-28 00:43:14 | INFO     | main:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-05-28 00:43:14 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-05-28 00:43:14 | INFO     | src.pnl_attribution:_calculate_actual_pnl:328 - Using total_option_return as actual PnL baseline
2025-05-28 00:43:14 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-05-28 00:43:14 | INFO     | main:run_full_analysis:133 - Step 4: Generating summary reports
2025-05-28 00:43:14 | INFO     | main:run_full_analysis:137 - Step 5: Preparing database export data
2025-05-28 00:43:14 | INFO     | src.data_processor:prepare_database_export_data:243 - Prepared database export data: 160 records, 45 columns
2025-05-28 00:43:14 | INFO     | main:run_full_analysis:143 - Step 6: Saving results to Excel
2025-05-28 00:43:14 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_detail_20250528_004314.xlsx
2025-05-28 00:43:14 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_summary_20250528_004314.xlsx
2025-05-28 00:43:14 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/database_export.xlsx
2025-05-28 00:43:14 | INFO     | main:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-05-28 00:44:08 | INFO     | main:__init__:49 - Option PnL Attribution System initialized
2025-05-28 00:44:08 | INFO     | src.data_processor:create_sample_data:61 - Creating sample data files
2025-05-28 00:44:08 | INFO     | src.data_processor:_create_sample_consolidated_data:126 - Created 160 sample option contracts in data/input/consolidated_option_data.xlsx
2025-05-28 00:44:08 | INFO     | src.data_processor:create_sample_data:69 - Sample data files created successfully
2025-05-28 00:44:08 | INFO     | main:create_sample_data:305 - Sample data created successfully
2025-05-28 00:44:08 | INFO     | main:run_full_analysis:106 - Starting full PnL attribution analysis
2025-05-28 00:44:08 | INFO     | main:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-05-28 00:44:08 | INFO     | src.data_processor:load_consolidated_data:138 - Loaded 160 consolidated option records
2025-05-28 00:44:08 | INFO     | src.data_processor:merge_data:189 - Processed consolidated data: 160 records
2025-05-28 00:44:08 | INFO     | main:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-05-28 00:44:08 | INFO     | main:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-05-28 00:44:08 | INFO     | main:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-05-28 00:44:08 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-05-28 00:44:08 | INFO     | src.pnl_attribution:_calculate_actual_pnl:328 - Using total_option_return as actual PnL baseline
2025-05-28 00:44:08 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-05-28 00:44:08 | INFO     | main:run_full_analysis:133 - Step 4: Generating summary reports
2025-05-28 00:44:08 | INFO     | main:run_full_analysis:137 - Step 5: Preparing database export data
2025-05-28 00:44:08 | INFO     | src.data_processor:prepare_database_export_data:243 - Prepared database export data: 160 records, 45 columns
2025-05-28 00:44:08 | INFO     | main:run_full_analysis:143 - Step 6: Saving results to Excel
2025-05-28 00:44:08 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_detail_20250528_004408.xlsx
2025-05-28 00:44:08 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_summary_20250528_004408.xlsx
2025-05-28 00:44:08 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/database_export.xlsx
2025-05-28 00:44:08 | INFO     | main:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-05-28 00:44:08 | INFO     | src.data_processor:__init__:33 - Data processor initialized
2025-05-28 00:44:08 | INFO     | src.data_processor:create_sample_data:61 - Creating sample data files
2025-05-28 00:44:08 | INFO     | src.data_processor:_create_sample_consolidated_data:126 - Created 160 sample option contracts in data/input/consolidated_option_data.xlsx
2025-05-28 00:44:08 | INFO     | src.data_processor:create_sample_data:69 - Sample data files created successfully
2025-05-28 00:44:08 | INFO     | src.data_processor:load_consolidated_data:138 - Loaded 160 consolidated option records
2025-05-28 00:44:17 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-05-28 00:44:17 | INFO     | src.data_processor:create_sample_data:61 - Creating sample data files
2025-05-28 00:44:17 | INFO     | src.data_processor:_create_sample_consolidated_data:126 - Created 160 sample option contracts in data/input/consolidated_option_data.xlsx
2025-05-28 00:44:17 | INFO     | src.data_processor:create_sample_data:69 - Sample data files created successfully
2025-05-28 00:44:17 | INFO     | __main__:create_sample_data:305 - Sample data created successfully
2025-05-28 00:44:24 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-05-28 00:44:24 | ERROR    | src.database_exporter:connect:78 - Failed to connect to database: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost' ([Errno 61] Connection refused)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-28 00:44:31 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-05-28 00:44:31 | INFO     | src.data_processor:load_consolidated_data:138 - Loaded 160 consolidated option records
2025-05-28 00:44:31 | INFO     | src.data_processor:load_consolidated_data:138 - Loaded 160 consolidated option records
2025-05-28 00:44:31 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-05-28 00:44:31 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-05-28 00:44:31 | INFO     | src.data_processor:load_consolidated_data:138 - Loaded 160 consolidated option records
2025-05-28 00:44:31 | INFO     | src.data_processor:merge_data:189 - Processed consolidated data: 160 records
2025-05-28 00:44:31 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-05-28 00:44:31 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-05-28 00:44:31 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-05-28 00:44:31 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-05-28 00:44:31 | INFO     | src.pnl_attribution:_calculate_actual_pnl:328 - Using total_option_return as actual PnL baseline
2025-05-28 00:44:31 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-05-28 00:44:31 | INFO     | __main__:run_full_analysis:133 - Step 4: Generating summary reports
2025-05-28 00:44:31 | INFO     | __main__:run_full_analysis:137 - Step 5: Preparing database export data
2025-05-28 00:44:31 | INFO     | src.data_processor:prepare_database_export_data:243 - Prepared database export data: 160 records, 45 columns
2025-05-28 00:44:31 | INFO     | __main__:run_full_analysis:143 - Step 6: Saving results to Excel
2025-05-28 00:44:31 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_detail_20250528_004431.xlsx
2025-05-28 00:44:31 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_summary_20250528_004431.xlsx
2025-05-28 00:44:31 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/database_export.xlsx
2025-05-28 00:44:31 | INFO     | __main__:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-05-28 00:46:32 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-05-28 00:46:32 | INFO     | src.data_processor:create_sample_data:61 - Creating sample data files
2025-05-28 00:46:32 | INFO     | src.data_processor:_create_sample_consolidated_data:126 - Created 160 sample option contracts in data/input/consolidated_option_data.xlsx
2025-05-28 00:46:32 | INFO     | src.data_processor:create_sample_data:69 - Sample data files created successfully
2025-05-28 00:46:32 | INFO     | __main__:create_sample_data:305 - Sample data created successfully
2025-05-28 00:46:40 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-05-28 00:46:40 | ERROR    | src.database_exporter:connect:78 - Failed to connect to database: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost' ([Errno 61] Connection refused)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-28 00:46:49 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-05-28 00:46:50 | INFO     | src.data_processor:load_consolidated_data:138 - Loaded 160 consolidated option records
2025-05-28 00:46:50 | INFO     | src.data_processor:load_consolidated_data:138 - Loaded 160 consolidated option records
2025-05-28 00:46:50 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-05-28 00:46:50 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-05-28 00:46:50 | INFO     | src.data_processor:load_consolidated_data:138 - Loaded 160 consolidated option records
2025-05-28 00:46:50 | INFO     | src.data_processor:merge_data:189 - Processed consolidated data: 160 records
2025-05-28 00:46:50 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-05-28 00:46:50 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-05-28 00:46:50 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-05-28 00:46:50 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-05-28 00:46:50 | INFO     | src.pnl_attribution:_calculate_actual_pnl:328 - Using total_option_return as actual PnL baseline
2025-05-28 00:46:50 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-05-28 00:46:50 | INFO     | __main__:run_full_analysis:133 - Step 4: Generating summary reports
2025-05-28 00:46:50 | INFO     | __main__:run_full_analysis:137 - Step 5: Preparing database export data
2025-05-28 00:46:50 | INFO     | src.data_processor:prepare_database_export_data:243 - Prepared database export data: 160 records, 45 columns
2025-05-28 00:46:50 | INFO     | __main__:run_full_analysis:143 - Step 6: Saving results to Excel
2025-05-28 00:46:50 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_detail_20250528_004650.xlsx
2025-05-28 00:46:50 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_summary_20250528_004650.xlsx
2025-05-28 00:46:50 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/database_export.xlsx
2025-05-28 00:46:50 | INFO     | __main__:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-05-28 00:47:00 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-05-28 00:47:00 | INFO     | src.data_processor:load_consolidated_data:138 - Loaded 160 consolidated option records
2025-05-28 00:47:00 | INFO     | src.data_processor:load_consolidated_data:138 - Loaded 160 consolidated option records
2025-05-28 00:47:00 | INFO     | __main__:run_visualization_analysis:345 - Starting visualization analysis
2025-05-28 00:47:00 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-05-28 00:47:00 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-05-28 00:47:00 | INFO     | src.data_processor:load_consolidated_data:138 - Loaded 160 consolidated option records
2025-05-28 00:47:00 | INFO     | src.data_processor:merge_data:189 - Processed consolidated data: 160 records
2025-05-28 00:47:00 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-05-28 00:47:00 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-05-28 00:47:00 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-05-28 00:47:00 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-05-28 00:47:00 | INFO     | src.pnl_attribution:_calculate_actual_pnl:328 - Using total_option_return as actual PnL baseline
2025-05-28 00:47:00 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-05-28 00:47:00 | INFO     | __main__:run_full_analysis:133 - Step 4: Generating summary reports
2025-05-28 00:47:00 | INFO     | __main__:run_full_analysis:137 - Step 5: Preparing database export data
2025-05-28 00:47:00 | INFO     | src.data_processor:prepare_database_export_data:243 - Prepared database export data: 160 records, 45 columns
2025-05-28 00:47:00 | INFO     | __main__:run_full_analysis:143 - Step 6: Saving results to Excel
2025-05-28 00:47:00 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_detail_20250528_004700.xlsx
2025-05-28 00:47:00 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_summary_20250528_004700.xlsx
2025-05-28 00:47:00 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/database_export.xlsx
2025-05-28 00:47:00 | INFO     | __main__:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-05-28 00:47:00 | INFO     | __main__:run_visualization_analysis:360 - Generating visualization charts
2025-05-28 00:47:00 | INFO     | src.visualization:generate_all_charts:553 - Generating all visualization charts
2025-05-28 00:47:00 | INFO     | src.visualization:create_pnl_attribution_stacked_chart:72 - Creating PnL attribution stacked chart grouped by underlying_code
2025-05-28 00:47:00 | INFO     | src.visualization:create_pnl_attribution_stacked_chart:146 - PnL attribution stacked chart saved to data/output/charts/pnl_attribution_stacked_underlying_code_20250528_004700.png
2025-05-28 00:47:00 | INFO     | src.visualization:create_pnl_attribution_stacked_chart:72 - Creating PnL attribution stacked chart grouped by portfolio
2025-05-28 00:47:00 | INFO     | src.visualization:create_pnl_attribution_stacked_chart:146 - PnL attribution stacked chart saved to data/output/charts/pnl_attribution_stacked_portfolio_20250528_004700.png
2025-05-28 00:47:00 | INFO     | src.visualization:create_greeks_heatmap:162 - Creating delta heatmap
2025-05-28 00:47:00 | INFO     | src.visualization:create_greeks_heatmap:225 - Greeks heatmap saved to data/output/charts/greeks_heatmap_delta_20250528_004700.png
2025-05-28 00:47:00 | INFO     | src.visualization:create_greeks_heatmap:162 - Creating gamma heatmap
2025-05-28 00:47:01 | INFO     | src.visualization:create_greeks_heatmap:225 - Greeks heatmap saved to data/output/charts/greeks_heatmap_gamma_20250528_004700.png
2025-05-28 00:47:01 | INFO     | src.visualization:create_portfolio_risk_dashboard:242 - Creating portfolio risk dashboard
2025-05-28 00:47:01 | INFO     | src.visualization:create_portfolio_risk_dashboard:369 - Portfolio risk dashboard saved to data/output/charts/portfolio_risk_dashboard_20250528_004701.png
2025-05-28 00:47:01 | INFO     | src.visualization:create_position_direction_comparison:386 - Creating position direction comparison chart
2025-05-28 00:47:01 | ERROR    | src.visualization:generate_all_charts:588 - Error generating charts: Wedge sizes 'x' must be non negative values
2025-05-28 00:47:01 | INFO     | __main__:run_visualization_analysis:368 - Creating visualization report
2025-05-28 00:47:01 | INFO     | src.visualization:create_summary_report:605 - Creating visualization summary report
2025-05-28 00:47:01 | INFO     | src.visualization:create_summary_report:692 - Visualization report saved to data/output/charts/visualization_report_20250528_004701.md
2025-05-28 00:47:01 | INFO     | __main__:run_visualization_analysis:386 - Visualization analysis completed successfully
2025-05-28 00:47:30 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-05-28 00:47:30 | INFO     | src.data_processor:load_consolidated_data:138 - Loaded 160 consolidated option records
2025-05-28 00:47:30 | INFO     | src.data_processor:load_consolidated_data:138 - Loaded 160 consolidated option records
2025-05-28 00:47:30 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-05-28 00:47:30 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-05-28 00:47:30 | INFO     | src.data_processor:load_consolidated_data:138 - Loaded 160 consolidated option records
2025-05-28 00:47:30 | INFO     | src.data_processor:merge_data:189 - Processed consolidated data: 160 records
2025-05-28 00:47:30 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-05-28 00:47:30 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-05-28 00:47:30 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-05-28 00:47:30 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-05-28 00:47:30 | INFO     | src.pnl_attribution:_calculate_actual_pnl:328 - Using total_option_return as actual PnL baseline
2025-05-28 00:47:30 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-05-28 00:47:30 | INFO     | __main__:run_full_analysis:133 - Step 4: Generating summary reports
2025-05-28 00:47:30 | INFO     | __main__:run_full_analysis:137 - Step 5: Preparing database export data
2025-05-28 00:47:30 | INFO     | src.data_processor:prepare_database_export_data:243 - Prepared database export data: 160 records, 45 columns
2025-05-28 00:47:30 | INFO     | __main__:run_full_analysis:143 - Step 6: Saving results to Excel
2025-05-28 00:47:30 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_detail_20250528_004730.xlsx
2025-05-28 00:47:30 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_summary_20250528_004730.xlsx
2025-05-28 00:47:30 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/database_export.xlsx
2025-05-28 00:47:30 | INFO     | __main__:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-05-28 01:07:03 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-05-28 01:07:03 | ERROR    | src.database_exporter:connect:78 - Failed to connect to database: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost' ([Errno 61] Connection refused)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-28 01:16:58 | INFO     | main:__init__:49 - Option PnL Attribution System initialized
2025-05-28 01:16:58 | INFO     | src.data_processor:create_sample_data:61 - Creating sample data files
2025-05-28 01:16:59 | INFO     | src.data_processor:_create_sample_consolidated_data:126 - Created 160 sample option contracts in data/input/consolidated_option_data.xlsx
2025-05-28 01:16:59 | INFO     | src.data_processor:create_sample_data:69 - Sample data files created successfully
2025-05-28 01:16:59 | INFO     | main:create_sample_data:305 - Sample data created successfully
2025-05-28 01:16:59 | INFO     | main:run_full_analysis:106 - Starting full PnL attribution analysis
2025-05-28 01:16:59 | INFO     | main:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-05-28 01:16:59 | INFO     | src.data_processor:load_consolidated_data:138 - Loaded 160 consolidated option records
2025-05-28 01:16:59 | INFO     | src.data_processor:merge_data:189 - Processed consolidated data: 160 records
2025-05-28 01:16:59 | INFO     | main:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-05-28 01:16:59 | INFO     | main:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-05-28 01:16:59 | INFO     | main:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-05-28 01:16:59 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-05-28 01:16:59 | INFO     | src.pnl_attribution:_calculate_actual_pnl:328 - Using total_option_return as actual PnL baseline
2025-05-28 01:16:59 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-05-28 01:16:59 | INFO     | main:run_full_analysis:133 - Step 4: Generating summary reports
2025-05-28 01:16:59 | INFO     | main:run_full_analysis:137 - Step 5: Preparing database export data
2025-05-28 01:16:59 | INFO     | src.data_processor:prepare_database_export_data:243 - Prepared database export data: 160 records, 45 columns
2025-05-28 01:16:59 | INFO     | main:run_full_analysis:143 - Step 6: Saving results to Excel
2025-05-28 01:16:59 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_detail_20250528_011659.xlsx
2025-05-28 01:16:59 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_summary_20250528_011659.xlsx
2025-05-28 01:16:59 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/database_export.xlsx
2025-05-28 01:16:59 | INFO     | main:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-05-28 01:17:54 | INFO     | main:__init__:49 - Option PnL Attribution System initialized
2025-05-28 01:17:54 | INFO     | src.data_processor:create_sample_data:61 - Creating sample data files
2025-05-28 01:17:54 | INFO     | src.data_processor:_create_sample_consolidated_data:126 - Created 160 sample option contracts in data/input/consolidated_option_data.xlsx
2025-05-28 01:17:54 | INFO     | src.data_processor:create_sample_data:69 - Sample data files created successfully
2025-05-28 01:17:54 | INFO     | main:create_sample_data:305 - Sample data created successfully
2025-05-28 01:17:54 | INFO     | main:run_full_analysis:106 - Starting full PnL attribution analysis
2025-05-28 01:17:54 | INFO     | main:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-05-28 01:17:54 | INFO     | src.data_processor:load_consolidated_data:138 - Loaded 160 consolidated option records
2025-05-28 01:17:54 | INFO     | src.data_processor:merge_data:189 - Processed consolidated data: 160 records
2025-05-28 01:17:54 | INFO     | main:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-05-28 01:17:54 | INFO     | main:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-05-28 01:17:54 | INFO     | main:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-05-28 01:17:54 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-05-28 01:17:54 | INFO     | src.pnl_attribution:_calculate_actual_pnl:328 - Using total_option_return as actual PnL baseline
2025-05-28 01:17:54 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-05-28 01:17:54 | INFO     | main:run_full_analysis:133 - Step 4: Generating summary reports
2025-05-28 01:17:54 | INFO     | main:run_full_analysis:137 - Step 5: Preparing database export data
2025-05-28 01:17:54 | INFO     | src.data_processor:prepare_database_export_data:243 - Prepared database export data: 160 records, 45 columns
2025-05-28 01:17:54 | INFO     | main:run_full_analysis:143 - Step 6: Saving results to Excel
2025-05-28 01:17:54 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_detail_20250528_011754.xlsx
2025-05-28 01:17:54 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_summary_20250528_011754.xlsx
2025-05-28 01:17:54 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/database_export.xlsx
2025-05-28 01:17:54 | INFO     | main:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-05-28 01:17:54 | INFO     | src.data_processor:__init__:33 - Data processor initialized
2025-05-28 01:17:54 | INFO     | src.data_processor:create_sample_data:61 - Creating sample data files
2025-05-28 01:17:54 | INFO     | src.data_processor:_create_sample_consolidated_data:126 - Created 160 sample option contracts in data/input/consolidated_option_data.xlsx
2025-05-28 01:17:54 | INFO     | src.data_processor:create_sample_data:69 - Sample data files created successfully
2025-05-28 01:17:54 | INFO     | src.data_processor:load_consolidated_data:138 - Loaded 160 consolidated option records
2025-05-28 01:19:01 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-05-28 01:19:01 | ERROR    | src.database_exporter:connect:78 - Failed to connect to database: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost' ([Errno 61] Connection refused)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-28 01:19:02 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-05-28 01:19:02 | INFO     | src.data_processor:create_sample_data:61 - Creating sample data files
2025-05-28 01:19:02 | INFO     | src.data_processor:_create_sample_consolidated_data:126 - Created 160 sample option contracts in data/input/consolidated_option_data.xlsx
2025-05-28 01:19:02 | INFO     | src.data_processor:create_sample_data:69 - Sample data files created successfully
2025-05-28 01:19:02 | INFO     | __main__:create_sample_data:305 - Sample data created successfully
2025-06-17 20:48:03 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-06-17 20:48:03 | INFO     | src.data_processor:create_sample_data:61 - Creating sample data files
2025-06-17 20:48:03 | INFO     | src.data_processor:_create_sample_consolidated_data:126 - Created 160 sample option contracts in data/input/consolidated_option_data.xlsx
2025-06-17 20:48:03 | INFO     | src.data_processor:create_sample_data:69 - Sample data files created successfully
2025-06-17 20:48:03 | INFO     | __main__:create_sample_data:305 - Sample data created successfully
2025-06-17 20:48:13 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-06-17 20:48:13 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-17 20:48:13 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-17 20:48:13 | INFO     | src.data_processor:load_consolidated_data:138 - Loaded 160 consolidated option records
2025-06-17 20:48:13 | INFO     | src.data_processor:merge_data:189 - Processed consolidated data: 160 records
2025-06-17 20:48:13 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-17 20:48:13 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-17 20:48:13 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-17 20:48:13 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-17 20:48:13 | INFO     | src.pnl_attribution:_calculate_actual_pnl:329 - Using total_option_return as actual PnL baseline
2025-06-17 20:48:13 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-06-17 20:48:13 | INFO     | __main__:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-17 20:48:13 | INFO     | __main__:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-17 20:48:13 | INFO     | src.data_processor:prepare_database_export_data:243 - Prepared database export data: 160 records, 45 columns
2025-06-17 20:48:13 | INFO     | __main__:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-17 20:48:13 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_detail_20250617_204813.xlsx
2025-06-17 20:48:13 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_summary_20250617_204813.xlsx
2025-06-17 20:48:13 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/database_export.xlsx
2025-06-17 20:48:13 | INFO     | __main__:run_full_analysis:149 - Step 7: Exporting to database (mode: full)
2025-06-17 20:48:13 | ERROR    | src.database_exporter:connect:78 - Failed to connect to database: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost' ([Errno 61] Connection refused)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-17 20:48:13 | INFO     | src.database_exporter:close:462 - Database connection closed
2025-06-17 20:48:13 | INFO     | __main__:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-06-17 20:48:26 | INFO     | main:__init__:49 - Option PnL Attribution System initialized
2025-06-17 20:48:26 | INFO     | src.data_processor:create_sample_data:61 - Creating sample data files
2025-06-17 20:48:26 | INFO     | src.data_processor:_create_sample_consolidated_data:126 - Created 160 sample option contracts in data/input/consolidated_option_data.xlsx
2025-06-17 20:48:26 | INFO     | src.data_processor:create_sample_data:69 - Sample data files created successfully
2025-06-17 20:48:26 | INFO     | main:create_sample_data:305 - Sample data created successfully
2025-06-17 20:48:26 | INFO     | main:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-17 20:48:26 | INFO     | main:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-17 20:48:26 | INFO     | src.data_processor:load_consolidated_data:138 - Loaded 160 consolidated option records
2025-06-17 20:48:26 | INFO     | src.data_processor:merge_data:189 - Processed consolidated data: 160 records
2025-06-17 20:48:26 | INFO     | main:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-17 20:48:26 | INFO     | main:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-17 20:48:26 | INFO     | main:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-17 20:48:26 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-17 20:48:26 | INFO     | src.pnl_attribution:_calculate_actual_pnl:329 - Using total_option_return as actual PnL baseline
2025-06-17 20:48:26 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-06-17 20:48:26 | INFO     | main:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-17 20:48:26 | INFO     | main:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-17 20:48:26 | INFO     | src.data_processor:prepare_database_export_data:243 - Prepared database export data: 160 records, 45 columns
2025-06-17 20:48:26 | INFO     | main:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-17 20:48:26 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_detail_20250617_204826.xlsx
2025-06-17 20:48:26 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_summary_20250617_204826.xlsx
2025-06-17 20:48:26 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/database_export.xlsx
2025-06-17 20:48:26 | INFO     | main:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-06-17 20:48:26 | INFO     | src.data_processor:__init__:33 - Data processor initialized
2025-06-17 20:48:26 | INFO     | src.data_processor:create_sample_data:61 - Creating sample data files
2025-06-17 20:48:26 | INFO     | src.data_processor:_create_sample_consolidated_data:126 - Created 160 sample option contracts in data/input/consolidated_option_data.xlsx
2025-06-17 20:48:26 | INFO     | src.data_processor:create_sample_data:69 - Sample data files created successfully
2025-06-17 20:48:26 | INFO     | src.data_processor:load_consolidated_data:138 - Loaded 160 consolidated option records
2025-06-17 20:49:37 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-06-17 20:49:37 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-17 20:49:37 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-17 20:49:37 | INFO     | src.data_processor:load_consolidated_data:138 - Loaded 160 consolidated option records
2025-06-17 20:49:37 | INFO     | src.data_processor:merge_data:189 - Processed consolidated data: 160 records
2025-06-17 20:49:37 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-17 20:49:37 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-17 20:49:37 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-17 20:49:37 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-17 20:49:37 | INFO     | src.pnl_attribution:_calculate_actual_pnl:327 - Calculating actual PnL through option repricing
2025-06-17 20:49:37 | WARNING  | src.pnl_attribution:_calculate_actual_pnl:334 - total_option_return not found, calculating through repricing
2025-06-17 20:49:37 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-06-17 20:49:37 | INFO     | __main__:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-17 20:49:37 | INFO     | __main__:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-17 20:49:37 | INFO     | src.data_processor:prepare_database_export_data:243 - Prepared database export data: 160 records, 45 columns
2025-06-17 20:49:37 | INFO     | __main__:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-17 20:49:37 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_detail_20250617_204937.xlsx
2025-06-17 20:49:37 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_summary_20250617_204937.xlsx
2025-06-17 20:49:37 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/database_export.xlsx
2025-06-17 20:49:37 | INFO     | __main__:run_full_analysis:149 - Step 7: Exporting to database (mode: full)
2025-06-17 20:49:37 | ERROR    | src.database_exporter:connect:78 - Failed to connect to database: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost' ([Errno 61] Connection refused)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-17 20:49:37 | INFO     | src.database_exporter:close:462 - Database connection closed
2025-06-17 20:49:37 | INFO     | __main__:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-06-17 20:50:36 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-06-17 20:50:36 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-17 20:50:36 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-17 20:50:36 | INFO     | src.data_processor:load_consolidated_data:138 - Loaded 160 consolidated option records
2025-06-17 20:50:36 | INFO     | src.data_processor:merge_data:189 - Processed consolidated data: 160 records
2025-06-17 20:50:36 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-17 20:50:36 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-17 20:50:36 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-17 20:50:36 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-17 20:50:36 | INFO     | src.pnl_attribution:_calculate_actual_pnl:327 - Calculating actual PnL through option repricing
2025-06-17 20:50:36 | WARNING  | src.pnl_attribution:_calculate_actual_pnl:334 - total_option_return not found, calculating through repricing
2025-06-17 20:50:36 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-06-17 20:50:36 | INFO     | __main__:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-17 20:50:36 | INFO     | __main__:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-17 20:50:36 | INFO     | src.data_processor:prepare_database_export_data:243 - Prepared database export data: 160 records, 45 columns
2025-06-17 20:50:36 | INFO     | __main__:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-17 20:50:36 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_detail_20250617_205036.xlsx
2025-06-17 20:50:36 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_summary_20250617_205036.xlsx
2025-06-17 20:50:36 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/database_export.xlsx
2025-06-17 20:50:36 | INFO     | __main__:run_full_analysis:149 - Step 7: Exporting to database (mode: full)
2025-06-17 20:50:36 | ERROR    | src.database_exporter:connect:78 - Failed to connect to database: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost' ([Errno 61] Connection refused)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-17 20:50:36 | INFO     | src.database_exporter:close:462 - Database connection closed
2025-06-17 20:50:36 | INFO     | __main__:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-06-17 20:50:54 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-06-17 20:50:54 | INFO     | __main__:run_visualization_analysis:345 - Starting visualization analysis
2025-06-17 20:50:54 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-17 20:50:54 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-17 20:50:54 | INFO     | src.data_processor:load_consolidated_data:138 - Loaded 160 consolidated option records
2025-06-17 20:50:54 | INFO     | src.data_processor:merge_data:189 - Processed consolidated data: 160 records
2025-06-17 20:50:54 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-17 20:50:54 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-17 20:50:54 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-17 20:50:54 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-17 20:50:54 | INFO     | src.pnl_attribution:_calculate_actual_pnl:327 - Calculating actual PnL through option repricing
2025-06-17 20:50:54 | WARNING  | src.pnl_attribution:_calculate_actual_pnl:334 - total_option_return not found, calculating through repricing
2025-06-17 20:50:54 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-06-17 20:50:54 | INFO     | __main__:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-17 20:50:54 | INFO     | __main__:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-17 20:50:54 | INFO     | src.data_processor:prepare_database_export_data:243 - Prepared database export data: 160 records, 45 columns
2025-06-17 20:50:54 | INFO     | __main__:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-17 20:50:54 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_detail_20250617_205054.xlsx
2025-06-17 20:50:54 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_summary_20250617_205054.xlsx
2025-06-17 20:50:54 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/database_export.xlsx
2025-06-17 20:50:54 | INFO     | __main__:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-06-17 20:50:54 | INFO     | __main__:run_visualization_analysis:360 - Generating visualization charts
2025-06-17 20:50:54 | INFO     | src.visualization:generate_all_charts:553 - Generating all visualization charts
2025-06-17 20:50:54 | INFO     | src.visualization:create_pnl_attribution_stacked_chart:72 - Creating PnL attribution stacked chart grouped by underlying_code
2025-06-17 20:50:55 | INFO     | src.visualization:create_pnl_attribution_stacked_chart:146 - PnL attribution stacked chart saved to data/output/charts/pnl_attribution_stacked_underlying_code_20250617_205055.html
2025-06-17 20:50:55 | INFO     | src.visualization:create_pnl_attribution_stacked_chart:72 - Creating PnL attribution stacked chart grouped by portfolio
2025-06-17 20:50:55 | INFO     | src.visualization:create_pnl_attribution_stacked_chart:146 - PnL attribution stacked chart saved to data/output/charts/pnl_attribution_stacked_portfolio_20250617_205055.html
2025-06-17 20:50:55 | INFO     | src.visualization:create_greeks_heatmap:162 - Creating delta heatmap
2025-06-17 20:50:55 | INFO     | src.visualization:create_greeks_heatmap:225 - Greeks heatmap saved to data/output/charts/greeks_heatmap_delta_20250617_205055.html
2025-06-17 20:50:55 | INFO     | src.visualization:create_greeks_heatmap:162 - Creating gamma heatmap
2025-06-17 20:50:55 | INFO     | src.visualization:create_greeks_heatmap:225 - Greeks heatmap saved to data/output/charts/greeks_heatmap_gamma_20250617_205055.html
2025-06-17 20:50:55 | INFO     | src.visualization:create_portfolio_risk_dashboard:242 - Creating portfolio risk dashboard
2025-06-17 20:50:55 | INFO     | src.visualization:create_portfolio_risk_dashboard:369 - Portfolio risk dashboard saved to data/output/charts/portfolio_risk_dashboard_20250617_205055.html
2025-06-17 20:50:55 | INFO     | src.visualization:create_position_direction_comparison:386 - Creating position direction comparison chart
2025-06-17 20:50:55 | INFO     | src.visualization:create_position_direction_comparison:537 - Position direction comparison chart saved to data/output/charts/position_direction_comparison_20250617_205055.html
2025-06-17 20:50:55 | INFO     | src.visualization:generate_all_charts:585 - Generated 6 charts successfully
2025-06-17 20:50:55 | INFO     | __main__:run_visualization_analysis:368 - Creating visualization report
2025-06-17 20:50:55 | INFO     | src.visualization:create_summary_report:605 - Creating visualization summary report
2025-06-17 20:50:55 | INFO     | src.visualization:create_summary_report:692 - Visualization report saved to data/output/charts/visualization_report_20250617_205055.md
2025-06-17 20:50:55 | INFO     | __main__:run_visualization_analysis:386 - Visualization analysis completed successfully
2025-06-17 20:51:05 | INFO     | main:__init__:49 - Option PnL Attribution System initialized
2025-06-17 20:51:05 | INFO     | src.data_processor:create_sample_data:61 - Creating sample data files
2025-06-17 20:51:05 | INFO     | src.data_processor:_create_sample_consolidated_data:126 - Created 160 sample option contracts in data/input/consolidated_option_data.xlsx
2025-06-17 20:51:05 | INFO     | src.data_processor:create_sample_data:69 - Sample data files created successfully
2025-06-17 20:51:05 | INFO     | main:create_sample_data:305 - Sample data created successfully
2025-06-17 20:51:05 | INFO     | main:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-17 20:51:05 | INFO     | main:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-17 20:51:05 | INFO     | src.data_processor:load_consolidated_data:138 - Loaded 160 consolidated option records
2025-06-17 20:51:05 | INFO     | src.data_processor:merge_data:189 - Processed consolidated data: 160 records
2025-06-17 20:51:05 | INFO     | main:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-17 20:51:05 | INFO     | main:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-17 20:51:05 | INFO     | main:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-17 20:51:05 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-17 20:51:05 | INFO     | src.pnl_attribution:_calculate_actual_pnl:327 - Calculating actual PnL through option repricing
2025-06-17 20:51:05 | WARNING  | src.pnl_attribution:_calculate_actual_pnl:334 - total_option_return not found, calculating through repricing
2025-06-17 20:51:05 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-06-17 20:51:05 | INFO     | main:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-17 20:51:05 | INFO     | main:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-17 20:51:05 | INFO     | src.data_processor:prepare_database_export_data:243 - Prepared database export data: 160 records, 45 columns
2025-06-17 20:51:05 | INFO     | main:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-17 20:51:05 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_detail_20250617_205105.xlsx
2025-06-17 20:51:05 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_summary_20250617_205105.xlsx
2025-06-17 20:51:05 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/database_export.xlsx
2025-06-17 20:51:05 | INFO     | main:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-06-17 20:51:05 | INFO     | src.data_processor:__init__:33 - Data processor initialized
2025-06-17 20:51:05 | INFO     | src.data_processor:merge_data:189 - Processed consolidated data: 3 records
2025-06-17 20:51:05 | INFO     | src.data_processor:__init__:33 - Data processor initialized
2025-06-17 20:51:05 | INFO     | src.pnl_attribution:__init__:37 - PnL Attribution Calculator initialized
2025-06-17 20:51:05 | INFO     | src.database_exporter:__init__:34 - Database exporter initialized
2025-06-17 20:51:05 | INFO     | src.visualization:__init__:47 - Option visualization module initialized
2025-06-17 20:51:05 | INFO     | main:__init__:49 - Option PnL Attribution System initialized
2025-06-17 20:51:05 | INFO     | src.data_processor:create_sample_data:61 - Creating sample data files
2025-06-17 20:51:05 | INFO     | src.data_processor:_create_sample_consolidated_data:126 - Created 160 sample option contracts in data/input/consolidated_option_data.xlsx
2025-06-17 20:51:05 | INFO     | src.data_processor:create_sample_data:69 - Sample data files created successfully
2025-06-17 20:51:05 | INFO     | main:create_sample_data:305 - Sample data created successfully
2025-06-17 20:51:05 | INFO     | main:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-17 20:51:05 | INFO     | main:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-17 20:51:05 | INFO     | src.data_processor:load_consolidated_data:138 - Loaded 160 consolidated option records
2025-06-17 20:51:05 | INFO     | src.data_processor:merge_data:189 - Processed consolidated data: 160 records
2025-06-17 20:51:05 | INFO     | main:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-17 20:51:05 | INFO     | main:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-17 20:51:05 | INFO     | main:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-17 20:51:05 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-17 20:51:05 | INFO     | src.pnl_attribution:_calculate_actual_pnl:327 - Calculating actual PnL through option repricing
2025-06-17 20:51:05 | WARNING  | src.pnl_attribution:_calculate_actual_pnl:334 - total_option_return not found, calculating through repricing
2025-06-17 20:51:05 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-06-17 20:51:05 | INFO     | main:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-17 20:51:05 | INFO     | main:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-17 20:51:05 | INFO     | src.data_processor:prepare_database_export_data:243 - Prepared database export data: 160 records, 45 columns
2025-06-17 20:51:05 | INFO     | main:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-17 20:51:05 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_detail_20250617_205105.xlsx
2025-06-17 20:51:05 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_summary_20250617_205105.xlsx
2025-06-17 20:51:05 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/database_export.xlsx
2025-06-17 20:51:05 | INFO     | main:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-06-17 20:51:05 | INFO     | src.data_processor:__init__:33 - Data processor initialized
2025-06-17 20:51:05 | INFO     | src.data_processor:create_sample_data:61 - Creating sample data files
2025-06-17 20:51:05 | INFO     | src.data_processor:_create_sample_consolidated_data:126 - Created 160 sample option contracts in data/input/consolidated_option_data.xlsx
2025-06-17 20:51:05 | INFO     | src.data_processor:create_sample_data:69 - Sample data files created successfully
2025-06-17 20:51:05 | INFO     | src.data_processor:load_consolidated_data:138 - Loaded 160 consolidated option records
2025-06-17 20:54:58 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-06-17 20:54:58 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-17 20:54:58 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-17 20:54:58 | INFO     | src.data_processor:load_consolidated_data:138 - Loaded 160 consolidated option records
2025-06-17 20:54:58 | INFO     | src.data_processor:merge_data:189 - Processed consolidated data: 160 records
2025-06-17 20:54:58 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-17 20:54:58 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-17 20:54:58 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-17 20:54:58 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-17 20:54:58 | INFO     | src.pnl_attribution:_calculate_actual_pnl:327 - Calculating actual PnL through option repricing
2025-06-17 20:54:58 | WARNING  | src.pnl_attribution:_calculate_actual_pnl:334 - total_option_return not found, calculating through repricing
2025-06-17 20:54:58 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-06-17 20:54:58 | INFO     | __main__:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-17 20:54:58 | INFO     | __main__:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-17 20:54:58 | INFO     | src.data_processor:prepare_database_export_data:243 - Prepared database export data: 160 records, 45 columns
2025-06-17 20:54:58 | INFO     | __main__:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-17 20:54:58 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_detail_20250617_205458.xlsx
2025-06-17 20:54:58 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_summary_20250617_205458.xlsx
2025-06-17 20:54:58 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/database_export.xlsx
2025-06-17 20:54:58 | INFO     | __main__:run_full_analysis:149 - Step 7: Exporting to database (mode: full)
2025-06-17 20:54:58 | ERROR    | src.database_exporter:connect:78 - Failed to connect to database: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost' ([Errno 61] Connection refused)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-17 20:54:58 | INFO     | src.database_exporter:close:462 - Database connection closed
2025-06-17 20:54:58 | INFO     | __main__:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-06-17 20:55:17 | INFO     | main:__init__:49 - Option PnL Attribution System initialized
2025-06-17 20:55:17 | INFO     | src.data_processor:create_sample_data:61 - Creating sample data files
2025-06-17 20:55:17 | INFO     | src.data_processor:_create_sample_consolidated_data:126 - Created 160 sample option contracts in data/input/consolidated_option_data.xlsx
2025-06-17 20:55:17 | INFO     | src.data_processor:create_sample_data:69 - Sample data files created successfully
2025-06-17 20:55:17 | INFO     | main:create_sample_data:305 - Sample data created successfully
2025-06-17 20:55:17 | INFO     | main:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-17 20:55:17 | INFO     | main:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-17 20:55:17 | INFO     | src.data_processor:load_consolidated_data:138 - Loaded 160 consolidated option records
2025-06-17 20:55:17 | INFO     | src.data_processor:merge_data:189 - Processed consolidated data: 160 records
2025-06-17 20:55:17 | INFO     | main:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-17 20:55:17 | INFO     | main:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-17 20:55:17 | INFO     | main:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-17 20:55:17 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-17 20:55:17 | INFO     | src.pnl_attribution:_calculate_actual_pnl:327 - Calculating actual PnL through option repricing
2025-06-17 20:55:17 | WARNING  | src.pnl_attribution:_calculate_actual_pnl:334 - total_option_return not found, calculating through repricing
2025-06-17 20:55:17 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-06-17 20:55:17 | INFO     | main:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-17 20:55:17 | INFO     | main:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-17 20:55:17 | INFO     | src.data_processor:prepare_database_export_data:243 - Prepared database export data: 160 records, 45 columns
2025-06-17 20:55:17 | INFO     | main:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-17 20:55:17 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_detail_20250617_205517.xlsx
2025-06-17 20:55:17 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_summary_20250617_205517.xlsx
2025-06-17 20:55:17 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/database_export.xlsx
2025-06-17 20:55:17 | INFO     | main:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-06-17 20:55:17 | INFO     | src.data_processor:__init__:33 - Data processor initialized
2025-06-17 20:55:17 | INFO     | src.data_processor:merge_data:189 - Processed consolidated data: 3 records
2025-06-17 20:55:17 | INFO     | src.data_processor:__init__:33 - Data processor initialized
2025-06-17 20:55:17 | INFO     | src.pnl_attribution:__init__:37 - PnL Attribution Calculator initialized
2025-06-17 20:55:17 | INFO     | src.database_exporter:__init__:34 - Database exporter initialized
2025-06-17 20:55:17 | INFO     | src.visualization:__init__:47 - Option visualization module initialized
2025-06-17 20:55:17 | INFO     | main:__init__:49 - Option PnL Attribution System initialized
2025-06-17 20:55:17 | INFO     | src.data_processor:create_sample_data:61 - Creating sample data files
2025-06-17 20:55:17 | INFO     | src.data_processor:_create_sample_consolidated_data:126 - Created 160 sample option contracts in data/input/consolidated_option_data.xlsx
2025-06-17 20:55:17 | INFO     | src.data_processor:create_sample_data:69 - Sample data files created successfully
2025-06-17 20:55:17 | INFO     | main:create_sample_data:305 - Sample data created successfully
2025-06-17 20:55:17 | INFO     | main:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-17 20:55:17 | INFO     | main:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-17 20:55:17 | INFO     | src.data_processor:load_consolidated_data:138 - Loaded 160 consolidated option records
2025-06-17 20:55:17 | INFO     | src.data_processor:merge_data:189 - Processed consolidated data: 160 records
2025-06-17 20:55:17 | INFO     | main:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-17 20:55:17 | INFO     | main:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-17 20:55:17 | INFO     | main:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-17 20:55:17 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-17 20:55:17 | INFO     | src.pnl_attribution:_calculate_actual_pnl:327 - Calculating actual PnL through option repricing
2025-06-17 20:55:17 | WARNING  | src.pnl_attribution:_calculate_actual_pnl:334 - total_option_return not found, calculating through repricing
2025-06-17 20:55:17 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-06-17 20:55:17 | INFO     | main:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-17 20:55:17 | INFO     | main:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-17 20:55:17 | INFO     | src.data_processor:prepare_database_export_data:243 - Prepared database export data: 160 records, 45 columns
2025-06-17 20:55:17 | INFO     | main:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-17 20:55:17 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_detail_20250617_205517.xlsx
2025-06-17 20:55:17 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_summary_20250617_205517.xlsx
2025-06-17 20:55:17 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/database_export.xlsx
2025-06-17 20:55:17 | INFO     | main:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-06-17 20:55:17 | INFO     | src.data_processor:__init__:33 - Data processor initialized
2025-06-17 20:55:17 | INFO     | src.data_processor:create_sample_data:61 - Creating sample data files
2025-06-17 20:55:17 | INFO     | src.data_processor:_create_sample_consolidated_data:126 - Created 160 sample option contracts in data/input/consolidated_option_data.xlsx
2025-06-17 20:55:17 | INFO     | src.data_processor:create_sample_data:69 - Sample data files created successfully
2025-06-17 20:55:17 | INFO     | src.data_processor:load_consolidated_data:138 - Loaded 160 consolidated option records
2025-06-17 21:06:33 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-06-17 21:06:33 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-17 21:06:33 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-17 21:06:33 | INFO     | src.data_processor:load_consolidated_data:138 - Loaded 34 consolidated option records
2025-06-17 21:06:33 | INFO     | src.data_processor:merge_data:189 - Processed consolidated data: 34 records
2025-06-17 21:06:33 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-17 21:06:33 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-17 21:06:33 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-17 21:06:33 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-17 21:06:33 | INFO     | src.pnl_attribution:_calculate_actual_pnl:327 - Calculating actual PnL through option repricing
2025-06-17 21:06:33 | WARNING  | src.pnl_attribution:_calculate_actual_pnl:334 - total_option_return not found, calculating through repricing
2025-06-17 21:06:33 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-06-17 21:06:33 | INFO     | __main__:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-17 21:06:33 | INFO     | __main__:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-17 21:06:33 | INFO     | src.data_processor:prepare_database_export_data:243 - Prepared database export data: 34 records, 45 columns
2025-06-17 21:06:33 | INFO     | __main__:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-17 21:06:33 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_detail_20250617_210633.xlsx
2025-06-17 21:06:33 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_summary_20250617_210633.xlsx
2025-06-17 21:06:33 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/database_export.xlsx
2025-06-17 21:06:33 | INFO     | __main__:run_full_analysis:149 - Step 7: Exporting to database (mode: full)
2025-06-17 21:06:33 | ERROR    | src.database_exporter:connect:78 - Failed to connect to database: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost' ([Errno 61] Connection refused)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-17 21:06:33 | INFO     | src.database_exporter:close:462 - Database connection closed
2025-06-17 21:06:33 | INFO     | __main__:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-06-17 21:07:11 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-06-17 21:07:11 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-17 21:07:11 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-17 21:07:11 | INFO     | src.data_processor:load_consolidated_data:138 - Loaded 34 consolidated option records
2025-06-17 21:07:11 | INFO     | src.data_processor:merge_data:189 - Processed consolidated data: 34 records
2025-06-17 21:07:11 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-17 21:07:11 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-17 21:07:11 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-17 21:07:11 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-17 21:07:11 | INFO     | src.pnl_attribution:_calculate_actual_pnl:327 - Calculating actual PnL from actual option premium changes
2025-06-17 21:07:11 | INFO     | src.pnl_attribution:_calculate_actual_pnl:331 - Using total_option_return as actual PnL baseline
2025-06-17 21:07:11 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-06-17 21:07:11 | INFO     | __main__:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-17 21:07:11 | INFO     | __main__:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-17 21:07:11 | INFO     | src.data_processor:prepare_database_export_data:243 - Prepared database export data: 34 records, 45 columns
2025-06-17 21:07:11 | INFO     | __main__:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-17 21:07:11 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_detail_20250617_210711.xlsx
2025-06-17 21:07:11 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_summary_20250617_210711.xlsx
2025-06-17 21:07:11 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/database_export.xlsx
2025-06-17 21:07:11 | INFO     | __main__:run_full_analysis:149 - Step 7: Exporting to database (mode: full)
2025-06-17 21:07:11 | ERROR    | src.database_exporter:connect:78 - Failed to connect to database: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost' ([Errno 61] Connection refused)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-17 21:07:11 | INFO     | src.database_exporter:close:462 - Database connection closed
2025-06-17 21:07:11 | INFO     | __main__:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-06-17 21:07:39 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-06-17 21:07:39 | INFO     | __main__:run_visualization_analysis:345 - Starting visualization analysis
2025-06-17 21:07:39 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-17 21:07:39 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-17 21:07:39 | INFO     | src.data_processor:load_consolidated_data:138 - Loaded 34 consolidated option records
2025-06-17 21:07:39 | INFO     | src.data_processor:merge_data:189 - Processed consolidated data: 34 records
2025-06-17 21:07:39 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-17 21:07:39 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-17 21:07:39 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-17 21:07:39 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-17 21:07:39 | INFO     | src.pnl_attribution:_calculate_actual_pnl:327 - Calculating actual PnL from actual option premium changes
2025-06-17 21:07:39 | INFO     | src.pnl_attribution:_calculate_actual_pnl:331 - Using total_option_return as actual PnL baseline
2025-06-17 21:07:39 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-06-17 21:07:39 | INFO     | __main__:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-17 21:07:39 | INFO     | __main__:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-17 21:07:39 | INFO     | src.data_processor:prepare_database_export_data:243 - Prepared database export data: 34 records, 45 columns
2025-06-17 21:07:39 | INFO     | __main__:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-17 21:07:39 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_detail_20250617_210739.xlsx
2025-06-17 21:07:39 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_summary_20250617_210739.xlsx
2025-06-17 21:07:39 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/database_export.xlsx
2025-06-17 21:07:39 | INFO     | __main__:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-06-17 21:07:39 | INFO     | __main__:run_visualization_analysis:360 - Generating visualization charts
2025-06-17 21:07:39 | INFO     | src.visualization:generate_all_charts:553 - Generating all visualization charts
2025-06-17 21:07:39 | INFO     | src.visualization:create_pnl_attribution_stacked_chart:72 - Creating PnL attribution stacked chart grouped by underlying_code
2025-06-17 21:07:40 | INFO     | src.visualization:create_pnl_attribution_stacked_chart:146 - PnL attribution stacked chart saved to data/output/charts/pnl_attribution_stacked_underlying_code_20250617_210740.html
2025-06-17 21:07:40 | INFO     | src.visualization:create_pnl_attribution_stacked_chart:72 - Creating PnL attribution stacked chart grouped by portfolio
2025-06-17 21:07:40 | INFO     | src.visualization:create_pnl_attribution_stacked_chart:146 - PnL attribution stacked chart saved to data/output/charts/pnl_attribution_stacked_portfolio_20250617_210740.html
2025-06-17 21:07:40 | INFO     | src.visualization:create_greeks_heatmap:162 - Creating delta heatmap
2025-06-17 21:07:40 | INFO     | src.visualization:create_greeks_heatmap:225 - Greeks heatmap saved to data/output/charts/greeks_heatmap_delta_20250617_210740.html
2025-06-17 21:07:40 | INFO     | src.visualization:create_greeks_heatmap:162 - Creating gamma heatmap
2025-06-17 21:07:40 | INFO     | src.visualization:create_greeks_heatmap:225 - Greeks heatmap saved to data/output/charts/greeks_heatmap_gamma_20250617_210740.html
2025-06-17 21:07:40 | INFO     | src.visualization:create_portfolio_risk_dashboard:242 - Creating portfolio risk dashboard
2025-06-17 21:07:40 | INFO     | src.visualization:create_portfolio_risk_dashboard:369 - Portfolio risk dashboard saved to data/output/charts/portfolio_risk_dashboard_20250617_210740.html
2025-06-17 21:07:40 | INFO     | src.visualization:create_position_direction_comparison:386 - Creating position direction comparison chart
2025-06-17 21:07:40 | INFO     | src.visualization:create_position_direction_comparison:537 - Position direction comparison chart saved to data/output/charts/position_direction_comparison_20250617_210740.html
2025-06-17 21:07:40 | INFO     | src.visualization:generate_all_charts:585 - Generated 6 charts successfully
2025-06-17 21:07:40 | INFO     | __main__:run_visualization_analysis:368 - Creating visualization report
2025-06-17 21:07:40 | INFO     | src.visualization:create_summary_report:605 - Creating visualization summary report
2025-06-17 21:07:40 | INFO     | src.visualization:create_summary_report:692 - Visualization report saved to data/output/charts/visualization_report_20250617_210740.md
2025-06-17 21:07:40 | INFO     | __main__:run_visualization_analysis:386 - Visualization analysis completed successfully
2025-06-17 21:17:11 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-06-17 21:17:11 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-17 21:17:11 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-17 21:17:12 | INFO     | src.data_processor:load_consolidated_data:138 - Loaded 34 consolidated option records
2025-06-17 21:17:12 | INFO     | src.data_processor:merge_data:189 - Processed consolidated data: 34 records
2025-06-17 21:17:12 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-17 21:17:12 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-17 21:17:12 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-17 21:17:12 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-17 21:17:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for cu2505: -0.0662
2025-06-17 21:17:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for eb2505: -0.0576
2025-06-17 21:17:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for ag2505: -0.0924
2025-06-17 21:17:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for sc2505: -0.0589
2025-06-17 21:17:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for TA505: -0.0525
2025-06-17 21:17:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for ru2505: -0.0433
2025-06-17 21:17:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for RM505: 0.0077
2025-06-17 21:17:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for PF505: -0.0506
2025-06-17 21:17:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for m2505: 0.0192
2025-06-17 21:17:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for eg2505: -0.0516
2025-06-17 21:17:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for al2505: -0.0695
2025-06-17 21:17:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for MA505: -0.0628
2025-06-17 21:17:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for OI505: -0.0227
2025-06-17 21:17:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for SR505: -0.0225
2025-06-17 21:17:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for UR505: -0.0243
2025-06-17 21:17:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for i2505: -0.0380
2025-06-17 21:17:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for v2505: -0.0333
2025-06-17 21:17:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for pp2505: -0.0201
2025-06-17 21:17:12 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:477 - Estimated vol change for cu2505: 18.2959
2025-06-17 21:17:12 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:477 - Estimated vol change for eb2505: 13.8735
2025-06-17 21:17:12 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:477 - Estimated vol change for ag2505: 9.5415
2025-06-17 21:17:12 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:477 - Estimated vol change for sc2505: 17.2483
2025-06-17 21:17:12 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:477 - Estimated vol change for TA505: 8.8986
2025-06-17 21:17:12 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:477 - Estimated vol change for ru2505: 1.6783
2025-06-17 21:17:12 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:477 - Estimated vol change for RM505: 0.1033
2025-06-17 21:17:12 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:477 - Estimated vol change for PF505: 5.7191
2025-06-17 21:17:12 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:477 - Estimated vol change for m2505: 0.2940
2025-06-17 21:17:12 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:477 - Estimated vol change for eg2505: 4.4742
2025-06-17 21:17:12 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:477 - Estimated vol change for al2505: 3.3643
2025-06-17 21:17:12 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:477 - Estimated vol change for MA505: 2.1353
2025-06-17 21:17:12 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:477 - Estimated vol change for OI505: 1.7964
2025-06-17 21:17:12 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:477 - Estimated vol change for SR505: 1.3221
2025-06-17 21:17:12 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:477 - Estimated vol change for UR505: 0.6879
2025-06-17 21:17:12 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:477 - Estimated vol change for i2505: 2.2588
2025-06-17 21:17:12 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:477 - Estimated vol change for v2505: 3.2526
2025-06-17 21:17:12 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:477 - Estimated vol change for pp2505: 3.0899
2025-06-17 21:17:12 | INFO     | src.pnl_attribution:_calculate_actual_pnl:327 - Calculating actual PnL from actual option premium changes
2025-06-17 21:17:12 | INFO     | src.pnl_attribution:_calculate_actual_pnl:331 - Using total_option_return as actual PnL baseline
2025-06-17 21:17:12 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-06-17 21:17:12 | INFO     | __main__:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-17 21:17:12 | INFO     | __main__:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-17 21:17:12 | INFO     | src.data_processor:prepare_database_export_data:243 - Prepared database export data: 34 records, 45 columns
2025-06-17 21:17:12 | INFO     | __main__:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-17 21:17:12 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_detail_20250617_211712.xlsx
2025-06-17 21:17:12 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_summary_20250617_211712.xlsx
2025-06-17 21:17:12 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/database_export.xlsx
2025-06-17 21:17:12 | INFO     | __main__:run_full_analysis:149 - Step 7: Exporting to database (mode: full)
2025-06-17 21:17:12 | ERROR    | src.database_exporter:connect:78 - Failed to connect to database: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost' ([Errno 61] Connection refused)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-17 21:17:12 | INFO     | src.database_exporter:close:462 - Database connection closed
2025-06-17 21:17:12 | INFO     | __main__:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-06-17 21:18:08 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-06-17 21:18:08 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-17 21:18:08 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-17 21:18:08 | INFO     | src.data_processor:load_consolidated_data:138 - Loaded 34 consolidated option records
2025-06-17 21:18:08 | INFO     | src.data_processor:merge_data:189 - Processed consolidated data: 34 records
2025-06-17 21:18:08 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-17 21:18:08 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-17 21:18:08 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-17 21:18:08 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-17 21:18:08 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for cu2505: -0.0662
2025-06-17 21:18:08 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for eb2505: -0.0576
2025-06-17 21:18:08 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for ag2505: -0.0924
2025-06-17 21:18:08 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for sc2505: -0.0589
2025-06-17 21:18:08 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for TA505: -0.0525
2025-06-17 21:18:08 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for ru2505: -0.0433
2025-06-17 21:18:08 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for RM505: 0.0077
2025-06-17 21:18:08 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for PF505: -0.0506
2025-06-17 21:18:08 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for m2505: 0.0192
2025-06-17 21:18:08 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for eg2505: -0.0516
2025-06-17 21:18:08 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for al2505: -0.0695
2025-06-17 21:18:08 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for MA505: -0.0628
2025-06-17 21:18:08 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for OI505: -0.0227
2025-06-17 21:18:08 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for SR505: -0.0225
2025-06-17 21:18:08 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for UR505: -0.0243
2025-06-17 21:18:08 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for i2505: -0.0380
2025-06-17 21:18:08 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for v2505: -0.0333
2025-06-17 21:18:08 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for pp2505: -0.0201
2025-06-17 21:18:08 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:482 - Estimated vol change for cu2505: 0.0013 (option change: 36.6580)
2025-06-17 21:18:08 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:482 - Estimated vol change for eb2505: 0.0012 (option change: 27.8047)
2025-06-17 21:18:08 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:482 - Estimated vol change for ag2505: 0.0018 (option change: 19.1753)
2025-06-17 21:18:08 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:482 - Estimated vol change for sc2505: 0.0012 (option change: 34.5556)
2025-06-17 21:18:08 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:482 - Estimated vol change for TA505: 0.0010 (option change: 17.8496)
2025-06-17 21:18:08 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:482 - Estimated vol change for ru2505: 0.0009 (option change: 3.4000)
2025-06-17 21:18:08 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:482 - Estimated vol change for RM505: 0.0008 (option change: 0.2143)
2025-06-17 21:18:08 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:482 - Estimated vol change for PF505: 0.0010 (option change: 11.4889)
2025-06-17 21:18:08 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:482 - Estimated vol change for m2505: 0.0010 (option change: 0.6071)
2025-06-17 21:18:08 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:482 - Estimated vol change for eg2505: 0.0010 (option change: 9.0000)
2025-06-17 21:18:08 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:482 - Estimated vol change for al2505: 0.0014 (option change: 6.7982)
2025-06-17 21:18:08 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:482 - Estimated vol change for MA505: 0.0013 (option change: 4.3333)
2025-06-17 21:18:08 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:482 - Estimated vol change for OI505: 0.0005 (option change: 3.6154)
2025-06-17 21:18:08 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:482 - Estimated vol change for SR505: 0.0004 (option change: 2.6667)
2025-06-17 21:18:08 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:482 - Estimated vol change for UR505: 0.0005 (option change: 1.4000)
2025-06-17 21:18:08 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:482 - Estimated vol change for i2505: 0.0008 (option change: 4.5556)
2025-06-17 21:18:08 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:482 - Estimated vol change for v2505: 0.0007 (option change: 6.5385)
2025-06-17 21:18:08 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:482 - Estimated vol change for pp2505: 0.0004 (option change: 6.2000)
2025-06-17 21:18:08 | INFO     | src.pnl_attribution:_calculate_actual_pnl:327 - Calculating actual PnL from actual option premium changes
2025-06-17 21:18:08 | INFO     | src.pnl_attribution:_calculate_actual_pnl:331 - Using total_option_return as actual PnL baseline
2025-06-17 21:18:08 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-06-17 21:18:08 | INFO     | __main__:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-17 21:18:08 | INFO     | __main__:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-17 21:18:08 | INFO     | src.data_processor:prepare_database_export_data:243 - Prepared database export data: 34 records, 45 columns
2025-06-17 21:18:08 | INFO     | __main__:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-17 21:18:08 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_detail_20250617_211808.xlsx
2025-06-17 21:18:08 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_summary_20250617_211808.xlsx
2025-06-17 21:18:08 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/database_export.xlsx
2025-06-17 21:18:08 | INFO     | __main__:run_full_analysis:149 - Step 7: Exporting to database (mode: full)
2025-06-17 21:18:09 | ERROR    | src.database_exporter:connect:78 - Failed to connect to database: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost' ([Errno 61] Connection refused)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-17 21:18:09 | INFO     | src.database_exporter:close:462 - Database connection closed
2025-06-17 21:18:09 | INFO     | __main__:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-06-17 21:18:17 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-06-17 21:18:17 | INFO     | __main__:run_visualization_analysis:345 - Starting visualization analysis
2025-06-17 21:18:17 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-17 21:18:17 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-17 21:18:17 | INFO     | src.data_processor:load_consolidated_data:138 - Loaded 34 consolidated option records
2025-06-17 21:18:17 | INFO     | src.data_processor:merge_data:189 - Processed consolidated data: 34 records
2025-06-17 21:18:17 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-17 21:18:17 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-17 21:18:17 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-17 21:18:17 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-17 21:18:17 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for cu2505: -0.0662
2025-06-17 21:18:17 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for eb2505: -0.0576
2025-06-17 21:18:17 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for ag2505: -0.0924
2025-06-17 21:18:17 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for sc2505: -0.0589
2025-06-17 21:18:17 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for TA505: -0.0525
2025-06-17 21:18:17 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for ru2505: -0.0433
2025-06-17 21:18:17 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for RM505: 0.0077
2025-06-17 21:18:17 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for PF505: -0.0506
2025-06-17 21:18:17 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for m2505: 0.0192
2025-06-17 21:18:17 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for eg2505: -0.0516
2025-06-17 21:18:17 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for al2505: -0.0695
2025-06-17 21:18:17 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for MA505: -0.0628
2025-06-17 21:18:17 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for OI505: -0.0227
2025-06-17 21:18:17 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for SR505: -0.0225
2025-06-17 21:18:17 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for UR505: -0.0243
2025-06-17 21:18:17 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for i2505: -0.0380
2025-06-17 21:18:17 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for v2505: -0.0333
2025-06-17 21:18:17 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:436 - Calculated actual price change for pp2505: -0.0201
2025-06-17 21:18:17 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:482 - Estimated vol change for cu2505: 0.0013 (option change: 36.6580)
2025-06-17 21:18:17 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:482 - Estimated vol change for eb2505: 0.0012 (option change: 27.8047)
2025-06-17 21:18:17 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:482 - Estimated vol change for ag2505: 0.0018 (option change: 19.1753)
2025-06-17 21:18:17 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:482 - Estimated vol change for sc2505: 0.0012 (option change: 34.5556)
2025-06-17 21:18:17 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:482 - Estimated vol change for TA505: 0.0010 (option change: 17.8496)
2025-06-17 21:18:17 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:482 - Estimated vol change for ru2505: 0.0009 (option change: 3.4000)
2025-06-17 21:18:17 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:482 - Estimated vol change for RM505: 0.0008 (option change: 0.2143)
2025-06-17 21:18:17 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:482 - Estimated vol change for PF505: 0.0010 (option change: 11.4889)
2025-06-17 21:18:17 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:482 - Estimated vol change for m2505: 0.0010 (option change: 0.6071)
2025-06-17 21:18:17 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:482 - Estimated vol change for eg2505: 0.0010 (option change: 9.0000)
2025-06-17 21:18:17 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:482 - Estimated vol change for al2505: 0.0014 (option change: 6.7982)
2025-06-17 21:18:17 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:482 - Estimated vol change for MA505: 0.0013 (option change: 4.3333)
2025-06-17 21:18:17 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:482 - Estimated vol change for OI505: 0.0005 (option change: 3.6154)
2025-06-17 21:18:17 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:482 - Estimated vol change for SR505: 0.0004 (option change: 2.6667)
2025-06-17 21:18:17 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:482 - Estimated vol change for UR505: 0.0005 (option change: 1.4000)
2025-06-17 21:18:17 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:482 - Estimated vol change for i2505: 0.0008 (option change: 4.5556)
2025-06-17 21:18:17 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:482 - Estimated vol change for v2505: 0.0007 (option change: 6.5385)
2025-06-17 21:18:17 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:482 - Estimated vol change for pp2505: 0.0004 (option change: 6.2000)
2025-06-17 21:18:17 | INFO     | src.pnl_attribution:_calculate_actual_pnl:327 - Calculating actual PnL from actual option premium changes
2025-06-17 21:18:17 | INFO     | src.pnl_attribution:_calculate_actual_pnl:331 - Using total_option_return as actual PnL baseline
2025-06-17 21:18:17 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-06-17 21:18:17 | INFO     | __main__:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-17 21:18:17 | INFO     | __main__:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-17 21:18:17 | INFO     | src.data_processor:prepare_database_export_data:243 - Prepared database export data: 34 records, 45 columns
2025-06-17 21:18:17 | INFO     | __main__:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-17 21:18:17 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_detail_20250617_211817.xlsx
2025-06-17 21:18:17 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_summary_20250617_211817.xlsx
2025-06-17 21:18:17 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/database_export.xlsx
2025-06-17 21:18:17 | INFO     | __main__:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-06-17 21:18:17 | INFO     | __main__:run_visualization_analysis:360 - Generating visualization charts
2025-06-17 21:18:17 | INFO     | src.visualization:generate_all_charts:553 - Generating all visualization charts
2025-06-17 21:18:17 | INFO     | src.visualization:create_pnl_attribution_stacked_chart:72 - Creating PnL attribution stacked chart grouped by underlying_code
2025-06-17 21:18:18 | INFO     | src.visualization:create_pnl_attribution_stacked_chart:146 - PnL attribution stacked chart saved to data/output/charts/pnl_attribution_stacked_underlying_code_20250617_211818.html
2025-06-17 21:18:18 | INFO     | src.visualization:create_pnl_attribution_stacked_chart:72 - Creating PnL attribution stacked chart grouped by portfolio
2025-06-17 21:18:18 | INFO     | src.visualization:create_pnl_attribution_stacked_chart:146 - PnL attribution stacked chart saved to data/output/charts/pnl_attribution_stacked_portfolio_20250617_211818.html
2025-06-17 21:18:18 | INFO     | src.visualization:create_greeks_heatmap:162 - Creating delta heatmap
2025-06-17 21:18:18 | INFO     | src.visualization:create_greeks_heatmap:225 - Greeks heatmap saved to data/output/charts/greeks_heatmap_delta_20250617_211818.html
2025-06-17 21:18:18 | INFO     | src.visualization:create_greeks_heatmap:162 - Creating gamma heatmap
2025-06-17 21:18:18 | INFO     | src.visualization:create_greeks_heatmap:225 - Greeks heatmap saved to data/output/charts/greeks_heatmap_gamma_20250617_211818.html
2025-06-17 21:18:18 | INFO     | src.visualization:create_portfolio_risk_dashboard:242 - Creating portfolio risk dashboard
2025-06-17 21:18:18 | INFO     | src.visualization:create_portfolio_risk_dashboard:369 - Portfolio risk dashboard saved to data/output/charts/portfolio_risk_dashboard_20250617_211818.html
2025-06-17 21:18:18 | INFO     | src.visualization:create_position_direction_comparison:386 - Creating position direction comparison chart
2025-06-17 21:18:18 | INFO     | src.visualization:create_position_direction_comparison:537 - Position direction comparison chart saved to data/output/charts/position_direction_comparison_20250617_211818.html
2025-06-17 21:18:18 | INFO     | src.visualization:generate_all_charts:585 - Generated 6 charts successfully
2025-06-17 21:18:18 | INFO     | __main__:run_visualization_analysis:368 - Creating visualization report
2025-06-17 21:18:18 | INFO     | src.visualization:create_summary_report:605 - Creating visualization summary report
2025-06-17 21:18:18 | INFO     | src.visualization:create_summary_report:692 - Visualization report saved to data/output/charts/visualization_report_20250617_211818.md
2025-06-17 21:18:18 | INFO     | __main__:run_visualization_analysis:386 - Visualization analysis completed successfully
2025-06-17 21:18:29 | INFO     | main:__init__:49 - Option PnL Attribution System initialized
2025-06-17 21:18:29 | INFO     | src.data_processor:create_sample_data:61 - Creating sample data files
2025-06-17 21:18:29 | INFO     | src.data_processor:_create_sample_consolidated_data:126 - Created 160 sample option contracts in data/input/consolidated_option_data.xlsx
2025-06-17 21:18:29 | INFO     | src.data_processor:create_sample_data:69 - Sample data files created successfully
2025-06-17 21:18:29 | INFO     | main:create_sample_data:305 - Sample data created successfully
2025-06-17 21:18:29 | INFO     | main:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-17 21:18:29 | INFO     | main:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-17 21:18:29 | INFO     | src.data_processor:load_consolidated_data:138 - Loaded 160 consolidated option records
2025-06-17 21:18:29 | INFO     | src.data_processor:merge_data:189 - Processed consolidated data: 160 records
2025-06-17 21:18:29 | INFO     | main:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-17 21:18:29 | INFO     | main:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-17 21:18:29 | INFO     | main:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-17 21:18:29 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-17 21:18:29 | INFO     | src.pnl_attribution:_calculate_actual_pnl:327 - Calculating actual PnL from actual option premium changes
2025-06-17 21:18:29 | INFO     | src.pnl_attribution:_calculate_actual_pnl:331 - Using total_option_return as actual PnL baseline
2025-06-17 21:18:29 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-06-17 21:18:29 | INFO     | main:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-17 21:18:29 | INFO     | main:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-17 21:18:29 | INFO     | src.data_processor:prepare_database_export_data:243 - Prepared database export data: 160 records, 45 columns
2025-06-17 21:18:29 | INFO     | main:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-17 21:18:29 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_detail_20250617_211829.xlsx
2025-06-17 21:18:29 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_summary_20250617_211829.xlsx
2025-06-17 21:18:29 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/database_export.xlsx
2025-06-17 21:18:29 | INFO     | main:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-06-17 21:18:29 | INFO     | src.data_processor:__init__:33 - Data processor initialized
2025-06-17 21:18:29 | INFO     | src.data_processor:merge_data:189 - Processed consolidated data: 3 records
2025-06-17 21:18:29 | INFO     | src.data_processor:__init__:33 - Data processor initialized
2025-06-17 21:18:29 | INFO     | src.pnl_attribution:__init__:37 - PnL Attribution Calculator initialized
2025-06-17 21:18:29 | INFO     | src.database_exporter:__init__:34 - Database exporter initialized
2025-06-17 21:18:29 | INFO     | src.visualization:__init__:47 - Option visualization module initialized
2025-06-17 21:18:29 | INFO     | main:__init__:49 - Option PnL Attribution System initialized
2025-06-17 21:18:29 | INFO     | src.data_processor:create_sample_data:61 - Creating sample data files
2025-06-17 21:18:29 | INFO     | src.data_processor:_create_sample_consolidated_data:126 - Created 160 sample option contracts in data/input/consolidated_option_data.xlsx
2025-06-17 21:18:29 | INFO     | src.data_processor:create_sample_data:69 - Sample data files created successfully
2025-06-17 21:18:29 | INFO     | main:create_sample_data:305 - Sample data created successfully
2025-06-17 21:18:29 | INFO     | main:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-17 21:18:29 | INFO     | main:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-17 21:18:29 | INFO     | src.data_processor:load_consolidated_data:138 - Loaded 160 consolidated option records
2025-06-17 21:18:29 | INFO     | src.data_processor:merge_data:189 - Processed consolidated data: 160 records
2025-06-17 21:18:29 | INFO     | main:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-17 21:18:29 | INFO     | main:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-17 21:18:29 | INFO     | main:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-17 21:18:29 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-17 21:18:29 | INFO     | src.pnl_attribution:_calculate_actual_pnl:327 - Calculating actual PnL from actual option premium changes
2025-06-17 21:18:29 | INFO     | src.pnl_attribution:_calculate_actual_pnl:331 - Using total_option_return as actual PnL baseline
2025-06-17 21:18:29 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-06-17 21:18:29 | INFO     | main:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-17 21:18:29 | INFO     | main:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-17 21:18:29 | INFO     | src.data_processor:prepare_database_export_data:243 - Prepared database export data: 160 records, 45 columns
2025-06-17 21:18:29 | INFO     | main:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-17 21:18:29 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_detail_20250617_211829.xlsx
2025-06-17 21:18:29 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/option_pnl_attribution_summary_20250617_211829.xlsx
2025-06-17 21:18:29 | INFO     | src.data_processor:save_to_excel:264 - Data saved to data/output/database_export.xlsx
2025-06-17 21:18:29 | INFO     | main:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-06-17 21:18:29 | INFO     | src.data_processor:__init__:33 - Data processor initialized
2025-06-17 21:18:29 | INFO     | src.data_processor:create_sample_data:61 - Creating sample data files
2025-06-17 21:18:29 | INFO     | src.data_processor:_create_sample_consolidated_data:126 - Created 160 sample option contracts in data/input/consolidated_option_data.xlsx
2025-06-17 21:18:29 | INFO     | src.data_processor:create_sample_data:69 - Sample data files created successfully
2025-06-17 21:18:29 | INFO     | src.data_processor:load_consolidated_data:138 - Loaded 160 consolidated option records
2025-06-17 21:44:41 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-06-17 21:44:41 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-17 21:44:41 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-17 21:44:41 | INFO     | src.data_processor:load_consolidated_data:137 - Loaded 160 consolidated option records
2025-06-17 21:44:41 | INFO     | src.data_processor:merge_data:188 - Processed consolidated data: 160 records
2025-06-17 21:44:41 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-17 21:44:41 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-17 21:44:41 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-17 21:44:41 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-17 21:44:41 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for 510050: 0.0125
2025-06-17 21:44:41 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for 510300: 0.0098
2025-06-17 21:44:41 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for 159919: 0.0179
2025-06-17 21:44:41 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for 159915: 0.0198
2025-06-17 21:44:41 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:503 - Calculated actual IV change for 510050: -2.0804 (from 2.0805 to 0.0001)
2025-06-17 21:44:41 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:503 - Calculated actual IV change for 510300: -2.0974 (from 3.0945 to 0.9971)
2025-06-17 21:44:41 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:503 - Calculated actual IV change for 159919: -2.3085 (from 2.3086 to 0.0001)
2025-06-17 21:44:41 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:503 - Calculated actual IV change for 159915: -2.0937 (from 2.6801 to 0.5864)
2025-06-17 21:44:41 | INFO     | src.pnl_attribution:_calculate_actual_pnl:327 - Calculating actual PnL from actual option premium changes
2025-06-17 21:44:41 | INFO     | src.pnl_attribution:_calculate_actual_pnl:331 - Using total_option_return as actual PnL baseline
2025-06-17 21:44:41 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-06-17 21:44:41 | INFO     | __main__:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-17 21:44:41 | INFO     | __main__:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-17 21:44:41 | INFO     | src.data_processor:prepare_database_export_data:242 - Prepared database export data: 160 records, 45 columns
2025-06-17 21:44:41 | INFO     | __main__:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-17 21:44:41 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_detail_20250617_214441.xlsx
2025-06-17 21:44:41 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_summary_20250617_214441.xlsx
2025-06-17 21:44:41 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/database_export.xlsx
2025-06-17 21:44:41 | INFO     | __main__:run_full_analysis:149 - Step 7: Exporting to database (mode: full)
2025-06-17 21:44:41 | ERROR    | src.database_exporter:connect:78 - Failed to connect to database: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost' ([Errno 61] Connection refused)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-17 21:44:41 | INFO     | src.database_exporter:close:462 - Database connection closed
2025-06-17 21:44:41 | INFO     | __main__:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-06-17 21:46:03 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-06-17 21:46:03 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-17 21:46:03 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-17 21:46:04 | INFO     | src.data_processor:load_consolidated_data:137 - Loaded 160 consolidated option records
2025-06-17 21:46:04 | INFO     | src.data_processor:merge_data:188 - Processed consolidated data: 160 records
2025-06-17 21:46:04 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-17 21:46:04 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-17 21:46:04 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-17 21:46:04 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-17 21:46:04 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for 510050: 0.0125
2025-06-17 21:46:04 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for 510300: 0.0098
2025-06-17 21:46:04 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for 159919: 0.0179
2025-06-17 21:46:04 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for 159915: 0.0198
2025-06-17 21:46:04 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:509 - Calculated actual IV change for 510050: -0.1000 (from 2.0805 to 0.0001)
2025-06-17 21:46:04 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:509 - Calculated actual IV change for 510300: -0.1000 (from 3.0945 to 0.9971)
2025-06-17 21:46:04 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:509 - Calculated actual IV change for 159919: -0.1000 (from 2.3086 to 0.0001)
2025-06-17 21:46:04 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:509 - Calculated actual IV change for 159915: -0.1000 (from 2.6801 to 0.5864)
2025-06-17 21:46:04 | INFO     | src.pnl_attribution:_calculate_actual_pnl:327 - Calculating actual PnL from actual option premium changes
2025-06-17 21:46:04 | INFO     | src.pnl_attribution:_calculate_actual_pnl:331 - Using total_option_return as actual PnL baseline
2025-06-17 21:46:04 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-06-17 21:46:04 | INFO     | __main__:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-17 21:46:04 | INFO     | __main__:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-17 21:46:04 | INFO     | src.data_processor:prepare_database_export_data:242 - Prepared database export data: 160 records, 45 columns
2025-06-17 21:46:04 | INFO     | __main__:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-17 21:46:04 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_detail_20250617_214604.xlsx
2025-06-17 21:46:04 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_summary_20250617_214604.xlsx
2025-06-17 21:46:04 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/database_export.xlsx
2025-06-17 21:46:04 | INFO     | __main__:run_full_analysis:149 - Step 7: Exporting to database (mode: full)
2025-06-17 21:46:04 | ERROR    | src.database_exporter:connect:78 - Failed to connect to database: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost' ([Errno 61] Connection refused)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-17 21:46:04 | INFO     | src.database_exporter:close:462 - Database connection closed
2025-06-17 21:46:04 | INFO     | __main__:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-06-17 21:47:21 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-06-17 21:47:21 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-17 21:47:21 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-17 21:47:21 | INFO     | src.data_processor:load_consolidated_data:137 - Loaded 160 consolidated option records
2025-06-17 21:47:21 | INFO     | src.data_processor:merge_data:188 - Processed consolidated data: 160 records
2025-06-17 21:47:21 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-17 21:47:21 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-17 21:47:21 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-17 21:47:21 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-17 21:47:21 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for 510050: 0.0125
2025-06-17 21:47:21 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for 510300: 0.0098
2025-06-17 21:47:21 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for 159919: 0.0179
2025-06-17 21:47:21 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for 159915: 0.0198
2025-06-17 21:47:21 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:503 - Calculated actual IV change for 510050: -2.0804 (from 2.0805 to 0.0001)
2025-06-17 21:47:21 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:503 - Calculated actual IV change for 510300: -2.0974 (from 3.0945 to 0.9971)
2025-06-17 21:47:21 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:503 - Calculated actual IV change for 159919: -2.3085 (from 2.3086 to 0.0001)
2025-06-17 21:47:21 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:503 - Calculated actual IV change for 159915: -2.0937 (from 2.6801 to 0.5864)
2025-06-17 21:47:21 | INFO     | src.pnl_attribution:_calculate_actual_pnl:327 - Calculating actual PnL from actual option premium changes
2025-06-17 21:47:21 | INFO     | src.pnl_attribution:_calculate_actual_pnl:331 - Using total_option_return as actual PnL baseline
2025-06-17 21:47:21 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-06-17 21:47:21 | INFO     | __main__:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-17 21:47:21 | INFO     | __main__:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-17 21:47:21 | INFO     | src.data_processor:prepare_database_export_data:242 - Prepared database export data: 160 records, 45 columns
2025-06-17 21:47:21 | INFO     | __main__:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-17 21:47:21 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_detail_20250617_214721.xlsx
2025-06-17 21:47:21 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_summary_20250617_214721.xlsx
2025-06-17 21:47:21 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/database_export.xlsx
2025-06-17 21:47:21 | INFO     | __main__:run_full_analysis:149 - Step 7: Exporting to database (mode: full)
2025-06-17 21:47:21 | ERROR    | src.database_exporter:connect:78 - Failed to connect to database: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost' ([Errno 61] Connection refused)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-17 21:47:21 | INFO     | src.database_exporter:close:462 - Database connection closed
2025-06-17 21:47:21 | INFO     | __main__:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-06-17 21:48:01 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-06-17 21:48:01 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-17 21:48:01 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-17 21:48:01 | INFO     | src.data_processor:load_consolidated_data:137 - Loaded 160 consolidated option records
2025-06-17 21:48:01 | INFO     | src.data_processor:merge_data:188 - Processed consolidated data: 160 records
2025-06-17 21:48:01 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-17 21:48:01 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-17 21:48:01 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-17 21:48:01 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-17 21:48:01 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for 510050: 0.0125
2025-06-17 21:48:01 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for 510300: 0.0098
2025-06-17 21:48:01 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for 159919: 0.0179
2025-06-17 21:48:01 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for 159915: 0.0198
2025-06-17 21:48:01 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:503 - Calculated actual IV change for 510050: -2.0804 (from 2.0805 to 0.0001)
2025-06-17 21:48:01 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:503 - Calculated actual IV change for 510300: -2.0974 (from 3.0945 to 0.9971)
2025-06-17 21:48:01 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:503 - Calculated actual IV change for 159919: -2.3085 (from 2.3086 to 0.0001)
2025-06-17 21:48:01 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:503 - Calculated actual IV change for 159915: -2.0937 (from 2.6801 to 0.5864)
2025-06-17 21:48:01 | INFO     | src.pnl_attribution:_calculate_actual_pnl:327 - Calculating actual PnL from actual option premium changes
2025-06-17 21:48:01 | INFO     | src.pnl_attribution:_calculate_actual_pnl:331 - Using total_option_return as actual PnL baseline
2025-06-17 21:48:01 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-06-17 21:48:01 | INFO     | __main__:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-17 21:48:01 | INFO     | __main__:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-17 21:48:01 | INFO     | src.data_processor:prepare_database_export_data:242 - Prepared database export data: 160 records, 45 columns
2025-06-17 21:48:01 | INFO     | __main__:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-17 21:48:01 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_detail_20250617_214801.xlsx
2025-06-17 21:48:01 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_summary_20250617_214801.xlsx
2025-06-17 21:48:01 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/database_export.xlsx
2025-06-17 21:48:01 | INFO     | __main__:run_full_analysis:149 - Step 7: Exporting to database (mode: full)
2025-06-17 21:48:01 | ERROR    | src.database_exporter:connect:78 - Failed to connect to database: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost' ([Errno 61] Connection refused)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-17 21:48:01 | INFO     | src.database_exporter:close:462 - Database connection closed
2025-06-17 21:48:01 | INFO     | __main__:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-06-17 21:48:59 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-06-17 21:48:59 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-17 21:48:59 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-17 21:48:59 | INFO     | src.data_processor:load_consolidated_data:137 - Loaded 160 consolidated option records
2025-06-17 21:48:59 | INFO     | src.data_processor:merge_data:188 - Processed consolidated data: 160 records
2025-06-17 21:48:59 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-17 21:48:59 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-17 21:48:59 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-17 21:48:59 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-17 21:48:59 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for 510050: 0.0125
2025-06-17 21:48:59 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for 510300: 0.0098
2025-06-17 21:48:59 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for 159919: 0.0179
2025-06-17 21:48:59 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for 159915: 0.0198
2025-06-17 21:48:59 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:463 - Using default vol bump for 510050: 0.01
2025-06-17 21:48:59 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:463 - Using default vol bump for 510300: 0.01
2025-06-17 21:48:59 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:463 - Using default vol bump for 159919: 0.01
2025-06-17 21:48:59 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:463 - Using default vol bump for 159915: 0.01
2025-06-17 21:48:59 | WARNING  | src.pnl_attribution:_calculate_actual_vol_changes:467 - ⚠️  警告：没有找到实际的隐含波动率变动数据，所有标的都使用默认的波动率冲击值进行计算
2025-06-17 21:48:59 | INFO     | src.pnl_attribution:_calculate_actual_pnl:327 - Calculating actual PnL from actual option premium changes
2025-06-17 21:48:59 | INFO     | src.pnl_attribution:_calculate_actual_pnl:331 - Using total_option_return as actual PnL baseline
2025-06-17 21:48:59 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-06-17 21:48:59 | INFO     | __main__:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-17 21:48:59 | INFO     | __main__:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-17 21:48:59 | INFO     | src.data_processor:prepare_database_export_data:242 - Prepared database export data: 160 records, 45 columns
2025-06-17 21:48:59 | INFO     | __main__:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-17 21:48:59 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_detail_20250617_214859.xlsx
2025-06-17 21:48:59 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_summary_20250617_214859.xlsx
2025-06-17 21:48:59 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/database_export.xlsx
2025-06-17 21:48:59 | INFO     | __main__:run_full_analysis:149 - Step 7: Exporting to database (mode: full)
2025-06-17 21:48:59 | ERROR    | src.database_exporter:connect:78 - Failed to connect to database: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost' ([Errno 61] Connection refused)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-17 21:48:59 | INFO     | src.database_exporter:close:462 - Database connection closed
2025-06-17 21:48:59 | INFO     | __main__:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-06-17 21:50:53 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-06-17 21:50:53 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-17 21:50:53 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-17 21:50:53 | INFO     | src.data_processor:load_consolidated_data:137 - Loaded 160 consolidated option records
2025-06-17 21:50:53 | INFO     | src.data_processor:merge_data:188 - Processed consolidated data: 160 records
2025-06-17 21:50:53 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-17 21:50:53 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-17 21:50:53 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-17 21:50:53 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-17 21:50:53 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for 510050: 0.0125
2025-06-17 21:50:53 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for 510300: 0.0098
2025-06-17 21:50:53 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for 159919: 0.0179
2025-06-17 21:50:53 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for 159915: 0.0198
2025-06-17 21:50:53 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for 510050: -2.0804 (from 2.0805 to 0.0001)
2025-06-17 21:50:53 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for 510300: -2.0974 (from 3.0945 to 0.9971)
2025-06-17 21:50:53 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for 159919: -2.3085 (from 2.3086 to 0.0001)
2025-06-17 21:50:53 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for 159915: -2.0937 (from 2.6801 to 0.5864)
2025-06-17 21:50:53 | INFO     | src.pnl_attribution:_calculate_actual_pnl:327 - Calculating actual PnL from actual option premium changes
2025-06-17 21:50:53 | INFO     | src.pnl_attribution:_calculate_actual_pnl:331 - Using total_option_return as actual PnL baseline
2025-06-17 21:50:53 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-06-17 21:50:53 | INFO     | __main__:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-17 21:50:53 | INFO     | __main__:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-17 21:50:53 | INFO     | src.data_processor:prepare_database_export_data:242 - Prepared database export data: 160 records, 45 columns
2025-06-17 21:50:53 | INFO     | __main__:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-17 21:50:53 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_detail_20250617_215053.xlsx
2025-06-17 21:50:53 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_summary_20250617_215053.xlsx
2025-06-17 21:50:54 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/database_export.xlsx
2025-06-17 21:50:54 | INFO     | __main__:run_full_analysis:149 - Step 7: Exporting to database (mode: full)
2025-06-17 21:50:54 | ERROR    | src.database_exporter:connect:78 - Failed to connect to database: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost' ([Errno 61] Connection refused)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-17 21:50:54 | INFO     | src.database_exporter:close:462 - Database connection closed
2025-06-17 21:50:54 | INFO     | __main__:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-06-17 23:31:07 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-06-17 23:31:07 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-17 23:31:07 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-17 23:31:07 | INFO     | src.data_processor:load_consolidated_data:137 - Loaded 34 consolidated option records
2025-06-17 23:31:07 | INFO     | src.data_processor:merge_data:188 - Processed consolidated data: 34 records
2025-06-17 23:31:07 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-17 23:31:07 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-17 23:31:07 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-17 23:31:07 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-17 23:31:07 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for cu2505: -0.0662
2025-06-17 23:31:07 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for eb2505: -0.0576
2025-06-17 23:31:07 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for ag2505: -0.0924
2025-06-17 23:31:07 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for sc2505: -0.0589
2025-06-17 23:31:07 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for TA505: -0.0525
2025-06-17 23:31:07 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for ru2505: -0.0433
2025-06-17 23:31:07 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for RM505: 0.0077
2025-06-17 23:31:07 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for PF505: -0.0506
2025-06-17 23:31:07 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for m2505: 0.0192
2025-06-17 23:31:07 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for eg2505: -0.0516
2025-06-17 23:31:07 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for al2505: -0.0695
2025-06-17 23:31:07 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for MA505: -0.0628
2025-06-17 23:31:07 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for OI505: -0.0227
2025-06-17 23:31:07 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for SR505: -0.0225
2025-06-17 23:31:07 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for UR505: -0.0243
2025-06-17 23:31:07 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for i2505: -0.0380
2025-06-17 23:31:07 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for v2505: -0.0333
2025-06-17 23:31:07 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for pp2505: -0.0201
2025-06-17 23:31:07 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for cu2505: 0.2651 (from 0.1651 to 0.4303)
2025-06-17 23:31:07 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for eb2505: 0.0728 (from 0.2577 to 0.3305)
2025-06-17 23:31:07 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for ag2505: 0.4125 (from 0.2232 to 0.6357)
2025-06-17 23:31:07 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for sc2505: 0.7292 (from 0.3722 to 1.1014)
2025-06-17 23:31:07 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for TA505: 0.2635 (from 0.1990 to 0.4625)
2025-06-17 23:31:07 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for ru2505: 0.1059 (from 0.1753 to 0.2812)
2025-06-17 23:31:07 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for RM505: 0.4213 (from 0.0001 to 0.4214)
2025-06-17 23:31:07 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for PF505: 0.2051 (from 0.1805 to 0.3856)
2025-06-17 23:31:07 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for m2505: 0.1427 (from 0.0001 to 0.1428)
2025-06-17 23:31:07 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for eg2505: -0.1575 (from 0.1576 to 0.0001)
2025-06-17 23:31:07 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for al2505: -0.1141 (from 0.1142 to 0.0001)
2025-06-17 23:31:07 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for MA505: -0.2313 (from 0.2314 to 0.0001)
2025-06-17 23:31:07 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for OI505: -0.0062 (from 0.2422 to 0.2360)
2025-06-17 23:31:07 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for SR505: -0.1492 (from 0.1493 to 0.0001)
2025-06-17 23:31:07 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for UR505: -0.0763 (from 0.2654 to 0.1891)
2025-06-17 23:31:07 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for i2505: -0.0079 (from 0.2419 to 0.2340)
2025-06-17 23:31:07 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for v2505: -0.0260 (from 0.1556 to 0.1296)
2025-06-17 23:31:07 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for pp2505: -0.0035 (from 0.1016 to 0.0981)
2025-06-17 23:31:07 | INFO     | src.pnl_attribution:_calculate_actual_pnl:327 - Calculating actual PnL from actual option premium changes
2025-06-17 23:31:07 | INFO     | src.pnl_attribution:_calculate_actual_pnl:331 - Using total_option_return as actual PnL baseline
2025-06-17 23:31:07 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-06-17 23:31:07 | INFO     | __main__:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-17 23:31:07 | INFO     | __main__:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-17 23:31:07 | INFO     | src.data_processor:prepare_database_export_data:242 - Prepared database export data: 34 records, 45 columns
2025-06-17 23:31:07 | INFO     | __main__:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-17 23:31:07 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_detail_20250617_233107.xlsx
2025-06-17 23:31:07 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_summary_20250617_233107.xlsx
2025-06-17 23:31:07 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/database_export.xlsx
2025-06-17 23:31:07 | INFO     | __main__:run_full_analysis:149 - Step 7: Exporting to database (mode: full)
2025-06-17 23:31:08 | ERROR    | src.database_exporter:connect:78 - Failed to connect to database: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost' ([Errno 61] Connection refused)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-17 23:31:08 | INFO     | src.database_exporter:close:462 - Database connection closed
2025-06-17 23:31:08 | INFO     | __main__:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-06-17 23:33:12 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-06-17 23:33:12 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-17 23:33:12 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-17 23:33:12 | INFO     | src.data_processor:load_consolidated_data:137 - Loaded 34 consolidated option records
2025-06-17 23:33:12 | INFO     | src.data_processor:merge_data:188 - Processed consolidated data: 34 records
2025-06-17 23:33:12 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-17 23:33:12 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-17 23:33:12 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-17 23:33:12 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-17 23:33:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for cu2505: -0.0662
2025-06-17 23:33:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for eb2505: -0.0576
2025-06-17 23:33:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for ag2505: -0.0924
2025-06-17 23:33:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for sc2505: -0.0589
2025-06-17 23:33:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for TA505: -0.0525
2025-06-17 23:33:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for ru2505: -0.0433
2025-06-17 23:33:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for RM505: 0.0077
2025-06-17 23:33:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for PF505: -0.0506
2025-06-17 23:33:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for m2505: 0.0192
2025-06-17 23:33:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for eg2505: -0.0516
2025-06-17 23:33:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for al2505: -0.0695
2025-06-17 23:33:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for MA505: -0.0628
2025-06-17 23:33:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for OI505: -0.0227
2025-06-17 23:33:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for SR505: -0.0225
2025-06-17 23:33:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for UR505: -0.0243
2025-06-17 23:33:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for i2505: -0.0380
2025-06-17 23:33:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for v2505: -0.0333
2025-06-17 23:33:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for pp2505: -0.0201
2025-06-17 23:33:12 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for cu2505: 0.2651 (from 0.1651 to 0.4303)
2025-06-17 23:33:12 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for eb2505: 0.0728 (from 0.2577 to 0.3305)
2025-06-17 23:33:12 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for ag2505: 0.4125 (from 0.2232 to 0.6357)
2025-06-17 23:33:12 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for sc2505: 0.7292 (from 0.3722 to 1.1014)
2025-06-17 23:33:12 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for TA505: 0.2635 (from 0.1990 to 0.4625)
2025-06-17 23:33:12 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for ru2505: 0.1059 (from 0.1753 to 0.2812)
2025-06-17 23:33:12 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for RM505: 0.4213 (from 0.0001 to 0.4214)
2025-06-17 23:33:12 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for PF505: 0.2051 (from 0.1805 to 0.3856)
2025-06-17 23:33:12 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for m2505: 0.1427 (from 0.0001 to 0.1428)
2025-06-17 23:33:12 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for eg2505: -0.1575 (from 0.1576 to 0.0001)
2025-06-17 23:33:12 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for al2505: -0.1141 (from 0.1142 to 0.0001)
2025-06-17 23:33:12 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for MA505: -0.2313 (from 0.2314 to 0.0001)
2025-06-17 23:33:12 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for OI505: -0.0062 (from 0.2422 to 0.2360)
2025-06-17 23:33:12 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for SR505: -0.1492 (from 0.1493 to 0.0001)
2025-06-17 23:33:12 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for UR505: -0.0763 (from 0.2654 to 0.1891)
2025-06-17 23:33:12 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for i2505: -0.0079 (from 0.2419 to 0.2340)
2025-06-17 23:33:12 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for v2505: -0.0260 (from 0.1556 to 0.1296)
2025-06-17 23:33:12 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for pp2505: -0.0035 (from 0.1016 to 0.0981)
2025-06-17 23:33:12 | INFO     | src.pnl_attribution:_calculate_actual_pnl:327 - Calculating actual PnL from actual option premium changes
2025-06-17 23:33:12 | INFO     | src.pnl_attribution:_calculate_actual_pnl:331 - Using total_option_return as actual PnL baseline
2025-06-17 23:33:12 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-06-17 23:33:12 | INFO     | __main__:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-17 23:33:12 | INFO     | __main__:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-17 23:33:12 | INFO     | src.data_processor:prepare_database_export_data:242 - Prepared database export data: 34 records, 45 columns
2025-06-17 23:33:12 | INFO     | __main__:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-17 23:33:12 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_detail_20250617_233312.xlsx
2025-06-17 23:33:12 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_summary_20250617_233312.xlsx
2025-06-17 23:33:12 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/database_export.xlsx
2025-06-17 23:33:12 | INFO     | __main__:run_full_analysis:149 - Step 7: Exporting to database (mode: full)
2025-06-17 23:33:12 | ERROR    | src.database_exporter:connect:78 - Failed to connect to database: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost' ([Errno 61] Connection refused)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-17 23:33:12 | INFO     | src.database_exporter:close:462 - Database connection closed
2025-06-17 23:33:12 | INFO     | __main__:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-06-17 23:39:15 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-06-17 23:39:15 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-17 23:39:15 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-17 23:39:15 | INFO     | src.data_processor:load_consolidated_data:137 - Loaded 34 consolidated option records
2025-06-17 23:39:15 | INFO     | src.data_processor:merge_data:188 - Processed consolidated data: 34 records
2025-06-17 23:39:15 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-17 23:39:15 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-17 23:39:15 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-17 23:39:15 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-17 23:39:15 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for cu2505: -0.0662
2025-06-17 23:39:15 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for eb2505: -0.0576
2025-06-17 23:39:15 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for ag2505: -0.0924
2025-06-17 23:39:15 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for sc2505: -0.0589
2025-06-17 23:39:15 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for TA505: -0.0525
2025-06-17 23:39:15 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for ru2505: -0.0433
2025-06-17 23:39:15 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for RM505: 0.0077
2025-06-17 23:39:15 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for PF505: -0.0506
2025-06-17 23:39:15 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for m2505: 0.0192
2025-06-17 23:39:15 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for eg2505: -0.0516
2025-06-17 23:39:15 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for al2505: -0.0695
2025-06-17 23:39:15 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for MA505: -0.0628
2025-06-17 23:39:15 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for OI505: -0.0227
2025-06-17 23:39:15 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for SR505: -0.0225
2025-06-17 23:39:15 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for UR505: -0.0243
2025-06-17 23:39:15 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for i2505: -0.0380
2025-06-17 23:39:15 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for v2505: -0.0333
2025-06-17 23:39:15 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:438 - Calculated actual price change for pp2505: -0.0201
2025-06-17 23:39:15 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for cu2505: 0.2651 (from 0.1651 to 0.4303)
2025-06-17 23:39:15 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for eb2505: 0.0728 (from 0.2577 to 0.3305)
2025-06-17 23:39:15 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for ag2505: 0.4125 (from 0.2232 to 0.6357)
2025-06-17 23:39:15 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for sc2505: 0.7292 (from 0.3722 to 1.1014)
2025-06-17 23:39:15 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for TA505: 0.2635 (from 0.1990 to 0.4625)
2025-06-17 23:39:15 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for ru2505: 0.1059 (from 0.1753 to 0.2812)
2025-06-17 23:39:15 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for RM505: 0.4213 (from 0.0001 to 0.4214)
2025-06-17 23:39:15 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for PF505: 0.2051 (from 0.1805 to 0.3856)
2025-06-17 23:39:15 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for m2505: 0.1427 (from 0.0001 to 0.1428)
2025-06-17 23:39:15 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for eg2505: -0.1575 (from 0.1576 to 0.0001)
2025-06-17 23:39:15 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for al2505: -0.1141 (from 0.1142 to 0.0001)
2025-06-17 23:39:15 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for MA505: -0.2313 (from 0.2314 to 0.0001)
2025-06-17 23:39:15 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for OI505: -0.0062 (from 0.2422 to 0.2360)
2025-06-17 23:39:15 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for SR505: -0.1492 (from 0.1493 to 0.0001)
2025-06-17 23:39:15 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for UR505: -0.0763 (from 0.2654 to 0.1891)
2025-06-17 23:39:15 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for i2505: -0.0079 (from 0.2419 to 0.2340)
2025-06-17 23:39:15 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for v2505: -0.0260 (from 0.1556 to 0.1296)
2025-06-17 23:39:15 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:506 - Calculated actual IV change for pp2505: -0.0035 (from 0.1016 to 0.0981)
2025-06-17 23:39:15 | INFO     | src.pnl_attribution:_calculate_actual_pnl:327 - Calculating actual PnL from actual option premium changes
2025-06-17 23:39:15 | INFO     | src.pnl_attribution:_calculate_actual_pnl:331 - Using total_option_return as actual PnL baseline
2025-06-17 23:39:15 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-06-17 23:39:15 | INFO     | __main__:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-17 23:39:15 | INFO     | __main__:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-17 23:39:15 | INFO     | src.data_processor:prepare_database_export_data:242 - Prepared database export data: 34 records, 45 columns
2025-06-17 23:39:15 | INFO     | __main__:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-17 23:39:15 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_detail_20250617_233915.xlsx
2025-06-17 23:39:15 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_summary_20250617_233915.xlsx
2025-06-17 23:39:15 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/database_export.xlsx
2025-06-17 23:39:15 | INFO     | __main__:run_full_analysis:149 - Step 7: Exporting to database (mode: full)
2025-06-17 23:39:15 | ERROR    | src.database_exporter:connect:78 - Failed to connect to database: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost' ([Errno 61] Connection refused)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-17 23:39:15 | INFO     | src.database_exporter:close:462 - Database connection closed
2025-06-17 23:39:15 | INFO     | __main__:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-06-18 00:17:54 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-06-18 00:17:54 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-18 00:17:54 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-18 00:17:54 | INFO     | src.data_processor:load_consolidated_data:137 - Loaded 34 consolidated option records
2025-06-18 00:17:54 | INFO     | src.data_processor:merge_data:188 - Processed consolidated data: 34 records
2025-06-18 00:17:54 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-18 00:17:54 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-18 00:17:54 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-18 00:17:54 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-18 00:17:54 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for cu2505: -0.0662
2025-06-18 00:17:54 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for eb2505: -0.0576
2025-06-18 00:17:54 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for ag2505: -0.0924
2025-06-18 00:17:54 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for sc2505: -0.0589
2025-06-18 00:17:54 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for TA505: -0.0525
2025-06-18 00:17:54 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for ru2505: -0.0433
2025-06-18 00:17:54 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for RM505: 0.0077
2025-06-18 00:17:54 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for PF505: -0.0506
2025-06-18 00:17:54 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for m2505: 0.0192
2025-06-18 00:17:54 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for eg2505: -0.0516
2025-06-18 00:17:54 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for al2505: -0.0695
2025-06-18 00:17:54 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for MA505: -0.0628
2025-06-18 00:17:54 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for OI505: -0.0227
2025-06-18 00:17:54 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for SR505: -0.0225
2025-06-18 00:17:54 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for UR505: -0.0243
2025-06-18 00:17:54 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for i2505: -0.0380
2025-06-18 00:17:54 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for v2505: -0.0333
2025-06-18 00:17:54 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for pp2505: -0.0201
2025-06-18 00:17:54 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for cu2505: 0.4894 (from 0.1651 to 0.6546)
2025-06-18 00:17:54 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for eb2505: 0.0444 (from 0.2577 to 0.3021)
2025-06-18 00:17:54 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for ag2505: 0.5867 (from 0.2232 to 0.8099)
2025-06-18 00:17:54 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for sc2505: 0.9278 (from 0.3722 to 1.3000)
2025-06-18 00:17:54 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for TA505: 0.3711 (from 0.1990 to 0.5701)
2025-06-18 00:17:54 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for ru2505: 0.2573 (from 0.1753 to 0.4326)
2025-06-18 00:17:54 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for RM505: 0.5514 (from 0.0001 to 0.5515)
2025-06-18 00:17:54 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for PF505: -0.1804 (from 0.1805 to 0.0001)
2025-06-18 00:17:54 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for m2505: 0.1201 (from 0.0001 to 0.1202)
2025-06-18 00:17:54 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for eg2505: -0.1575 (from 0.1576 to 0.0001)
2025-06-18 00:17:54 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for al2505: -0.1141 (from 0.1142 to 0.0001)
2025-06-18 00:17:54 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for MA505: -0.0549 (from 0.2314 to 0.1764)
2025-06-18 00:17:54 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for OI505: -0.0350 (from 0.2422 to 0.2072)
2025-06-18 00:17:54 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for SR505: -0.1492 (from 0.1493 to 0.0001)
2025-06-18 00:17:54 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for UR505: 0.2066 (from 0.2654 to 0.4721)
2025-06-18 00:17:54 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for i2505: -0.0425 (from 0.2419 to 0.1993)
2025-06-18 00:17:54 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for v2505: 0.3210 (from 0.1556 to 0.4766)
2025-06-18 00:17:54 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for pp2505: 0.0070 (from 0.1016 to 0.1086)
2025-06-18 00:17:54 | ERROR    | __main__:run_full_analysis:170 - PnL attribution analysis failed: 'option_type_numeric'
2025-06-18 00:18:35 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-06-18 00:18:35 | ERROR    | src.database_exporter:connect:78 - Failed to connect to database: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost' ([Errno 61] Connection refused)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-18 00:18:42 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-06-18 00:18:42 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-18 00:18:42 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-18 00:18:42 | INFO     | src.data_processor:load_consolidated_data:137 - Loaded 34 consolidated option records
2025-06-18 00:18:42 | INFO     | src.data_processor:merge_data:188 - Processed consolidated data: 34 records
2025-06-18 00:18:42 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-18 00:18:42 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-18 00:18:42 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-18 00:18:42 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-18 00:18:42 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for cu2505: -0.0662
2025-06-18 00:18:42 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for eb2505: -0.0576
2025-06-18 00:18:42 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for ag2505: -0.0924
2025-06-18 00:18:42 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for sc2505: -0.0589
2025-06-18 00:18:42 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for TA505: -0.0525
2025-06-18 00:18:42 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for ru2505: -0.0433
2025-06-18 00:18:42 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for RM505: 0.0077
2025-06-18 00:18:42 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for PF505: -0.0506
2025-06-18 00:18:42 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for m2505: 0.0192
2025-06-18 00:18:42 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for eg2505: -0.0516
2025-06-18 00:18:42 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for al2505: -0.0695
2025-06-18 00:18:42 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for MA505: -0.0628
2025-06-18 00:18:42 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for OI505: -0.0227
2025-06-18 00:18:42 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for SR505: -0.0225
2025-06-18 00:18:42 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for UR505: -0.0243
2025-06-18 00:18:42 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for i2505: -0.0380
2025-06-18 00:18:42 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for v2505: -0.0333
2025-06-18 00:18:42 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for pp2505: -0.0201
2025-06-18 00:18:42 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for cu2505: 0.4894 (from 0.1651 to 0.6546)
2025-06-18 00:18:42 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for eb2505: 0.0444 (from 0.2577 to 0.3021)
2025-06-18 00:18:42 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for ag2505: 0.5867 (from 0.2232 to 0.8099)
2025-06-18 00:18:42 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for sc2505: 0.9278 (from 0.3722 to 1.3000)
2025-06-18 00:18:42 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for TA505: 0.3711 (from 0.1990 to 0.5701)
2025-06-18 00:18:42 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for ru2505: 0.2573 (from 0.1753 to 0.4326)
2025-06-18 00:18:42 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for RM505: 0.5514 (from 0.0001 to 0.5515)
2025-06-18 00:18:42 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for PF505: -0.1804 (from 0.1805 to 0.0001)
2025-06-18 00:18:42 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for m2505: 0.1201 (from 0.0001 to 0.1202)
2025-06-18 00:18:42 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for eg2505: -0.1575 (from 0.1576 to 0.0001)
2025-06-18 00:18:42 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for al2505: -0.1141 (from 0.1142 to 0.0001)
2025-06-18 00:18:42 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for MA505: -0.0549 (from 0.2314 to 0.1764)
2025-06-18 00:18:42 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for OI505: -0.0350 (from 0.2422 to 0.2072)
2025-06-18 00:18:42 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for SR505: -0.1492 (from 0.1493 to 0.0001)
2025-06-18 00:18:42 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for UR505: 0.2066 (from 0.2654 to 0.4721)
2025-06-18 00:18:42 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for i2505: -0.0425 (from 0.2419 to 0.1993)
2025-06-18 00:18:42 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for v2505: 0.3210 (from 0.1556 to 0.4766)
2025-06-18 00:18:42 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for pp2505: 0.0070 (from 0.1016 to 0.1086)
2025-06-18 00:18:42 | ERROR    | __main__:run_full_analysis:170 - PnL attribution analysis failed: 'option_type_numeric'
2025-06-18 00:19:11 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-06-18 00:19:11 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-18 00:19:11 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-18 00:19:11 | INFO     | src.data_processor:load_consolidated_data:137 - Loaded 34 consolidated option records
2025-06-18 00:19:11 | INFO     | src.data_processor:merge_data:188 - Processed consolidated data: 34 records
2025-06-18 00:19:11 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-18 00:19:11 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-18 00:19:11 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-18 00:19:11 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-18 00:19:11 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for cu2505: -0.0662
2025-06-18 00:19:11 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for eb2505: -0.0576
2025-06-18 00:19:11 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for ag2505: -0.0924
2025-06-18 00:19:11 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for sc2505: -0.0589
2025-06-18 00:19:11 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for TA505: -0.0525
2025-06-18 00:19:11 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for ru2505: -0.0433
2025-06-18 00:19:11 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for RM505: 0.0077
2025-06-18 00:19:11 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for PF505: -0.0506
2025-06-18 00:19:11 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for m2505: 0.0192
2025-06-18 00:19:11 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for eg2505: -0.0516
2025-06-18 00:19:11 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for al2505: -0.0695
2025-06-18 00:19:11 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for MA505: -0.0628
2025-06-18 00:19:11 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for OI505: -0.0227
2025-06-18 00:19:11 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for SR505: -0.0225
2025-06-18 00:19:11 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for UR505: -0.0243
2025-06-18 00:19:11 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for i2505: -0.0380
2025-06-18 00:19:11 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for v2505: -0.0333
2025-06-18 00:19:11 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for pp2505: -0.0201
2025-06-18 00:19:11 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for cu2505: 0.4894 (from 0.1651 to 0.6546)
2025-06-18 00:19:11 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for eb2505: 0.0444 (from 0.2577 to 0.3021)
2025-06-18 00:19:11 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for ag2505: 0.5867 (from 0.2232 to 0.8099)
2025-06-18 00:19:11 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for sc2505: 0.9278 (from 0.3722 to 1.3000)
2025-06-18 00:19:11 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for TA505: 0.3711 (from 0.1990 to 0.5701)
2025-06-18 00:19:11 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for ru2505: 0.2573 (from 0.1753 to 0.4326)
2025-06-18 00:19:11 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for RM505: 0.5514 (from 0.0001 to 0.5515)
2025-06-18 00:19:11 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for PF505: -0.1804 (from 0.1805 to 0.0001)
2025-06-18 00:19:11 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for m2505: 0.1201 (from 0.0001 to 0.1202)
2025-06-18 00:19:11 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for eg2505: -0.1575 (from 0.1576 to 0.0001)
2025-06-18 00:19:11 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for al2505: -0.1141 (from 0.1142 to 0.0001)
2025-06-18 00:19:11 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for MA505: -0.0549 (from 0.2314 to 0.1764)
2025-06-18 00:19:11 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for OI505: -0.0350 (from 0.2422 to 0.2072)
2025-06-18 00:19:11 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for SR505: -0.1492 (from 0.1493 to 0.0001)
2025-06-18 00:19:11 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for UR505: 0.2066 (from 0.2654 to 0.4721)
2025-06-18 00:19:11 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for i2505: -0.0425 (from 0.2419 to 0.1993)
2025-06-18 00:19:11 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for v2505: 0.3210 (from 0.1556 to 0.4766)
2025-06-18 00:19:11 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for pp2505: 0.0070 (from 0.1016 to 0.1086)
2025-06-18 00:19:11 | INFO     | src.pnl_attribution:_calculate_actual_pnl:362 - Calculating actual PnL from actual option premium changes
2025-06-18 00:19:11 | INFO     | src.pnl_attribution:_calculate_actual_pnl:366 - Using total_option_return as actual PnL baseline
2025-06-18 00:19:11 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-06-18 00:19:11 | INFO     | __main__:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-18 00:19:11 | INFO     | __main__:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-18 00:19:11 | INFO     | src.data_processor:prepare_database_export_data:242 - Prepared database export data: 34 records, 50 columns
2025-06-18 00:19:11 | INFO     | __main__:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-18 00:19:11 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_detail_20250618_001911.xlsx
2025-06-18 00:19:11 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_summary_20250618_001911.xlsx
2025-06-18 00:19:11 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/database_export.xlsx
2025-06-18 00:19:11 | INFO     | __main__:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-06-18 00:19:18 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-06-18 00:19:18 | INFO     | __main__:run_visualization_analysis:345 - Starting visualization analysis
2025-06-18 00:19:18 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-18 00:19:18 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-18 00:19:18 | INFO     | src.data_processor:load_consolidated_data:137 - Loaded 34 consolidated option records
2025-06-18 00:19:18 | INFO     | src.data_processor:merge_data:188 - Processed consolidated data: 34 records
2025-06-18 00:19:18 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-18 00:19:18 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-18 00:19:18 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-18 00:19:18 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-18 00:19:18 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for cu2505: -0.0662
2025-06-18 00:19:18 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for eb2505: -0.0576
2025-06-18 00:19:18 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for ag2505: -0.0924
2025-06-18 00:19:18 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for sc2505: -0.0589
2025-06-18 00:19:18 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for TA505: -0.0525
2025-06-18 00:19:18 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for ru2505: -0.0433
2025-06-18 00:19:18 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for RM505: 0.0077
2025-06-18 00:19:18 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for PF505: -0.0506
2025-06-18 00:19:18 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for m2505: 0.0192
2025-06-18 00:19:18 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for eg2505: -0.0516
2025-06-18 00:19:18 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for al2505: -0.0695
2025-06-18 00:19:18 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for MA505: -0.0628
2025-06-18 00:19:18 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for OI505: -0.0227
2025-06-18 00:19:18 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for SR505: -0.0225
2025-06-18 00:19:18 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for UR505: -0.0243
2025-06-18 00:19:18 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for i2505: -0.0380
2025-06-18 00:19:18 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for v2505: -0.0333
2025-06-18 00:19:18 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for pp2505: -0.0201
2025-06-18 00:19:18 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for cu2505: 0.4894 (from 0.1651 to 0.6546)
2025-06-18 00:19:18 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for eb2505: 0.0444 (from 0.2577 to 0.3021)
2025-06-18 00:19:18 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for ag2505: 0.5867 (from 0.2232 to 0.8099)
2025-06-18 00:19:18 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for sc2505: 0.9278 (from 0.3722 to 1.3000)
2025-06-18 00:19:18 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for TA505: 0.3711 (from 0.1990 to 0.5701)
2025-06-18 00:19:18 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for ru2505: 0.2573 (from 0.1753 to 0.4326)
2025-06-18 00:19:18 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for RM505: 0.5514 (from 0.0001 to 0.5515)
2025-06-18 00:19:18 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for PF505: -0.1804 (from 0.1805 to 0.0001)
2025-06-18 00:19:18 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for m2505: 0.1201 (from 0.0001 to 0.1202)
2025-06-18 00:19:18 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for eg2505: -0.1575 (from 0.1576 to 0.0001)
2025-06-18 00:19:18 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for al2505: -0.1141 (from 0.1142 to 0.0001)
2025-06-18 00:19:18 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for MA505: -0.0549 (from 0.2314 to 0.1764)
2025-06-18 00:19:18 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for OI505: -0.0350 (from 0.2422 to 0.2072)
2025-06-18 00:19:18 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for SR505: -0.1492 (from 0.1493 to 0.0001)
2025-06-18 00:19:18 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for UR505: 0.2066 (from 0.2654 to 0.4721)
2025-06-18 00:19:18 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for i2505: -0.0425 (from 0.2419 to 0.1993)
2025-06-18 00:19:18 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for v2505: 0.3210 (from 0.1556 to 0.4766)
2025-06-18 00:19:18 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for pp2505: 0.0070 (from 0.1016 to 0.1086)
2025-06-18 00:19:18 | INFO     | src.pnl_attribution:_calculate_actual_pnl:362 - Calculating actual PnL from actual option premium changes
2025-06-18 00:19:18 | INFO     | src.pnl_attribution:_calculate_actual_pnl:366 - Using total_option_return as actual PnL baseline
2025-06-18 00:19:18 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-06-18 00:19:18 | INFO     | __main__:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-18 00:19:18 | INFO     | __main__:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-18 00:19:18 | INFO     | src.data_processor:prepare_database_export_data:242 - Prepared database export data: 34 records, 50 columns
2025-06-18 00:19:18 | INFO     | __main__:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-18 00:19:18 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_detail_20250618_001918.xlsx
2025-06-18 00:19:18 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_summary_20250618_001918.xlsx
2025-06-18 00:19:18 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/database_export.xlsx
2025-06-18 00:19:18 | INFO     | __main__:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-06-18 00:19:18 | INFO     | __main__:run_visualization_analysis:360 - Generating visualization charts
2025-06-18 00:19:18 | INFO     | src.visualization:generate_all_charts:553 - Generating all visualization charts
2025-06-18 00:19:18 | INFO     | src.visualization:create_pnl_attribution_stacked_chart:72 - Creating PnL attribution stacked chart grouped by underlying_code
2025-06-18 00:19:18 | INFO     | src.visualization:create_pnl_attribution_stacked_chart:146 - PnL attribution stacked chart saved to data/output/charts/pnl_attribution_stacked_underlying_code_20250618_001918.html
2025-06-18 00:19:18 | INFO     | src.visualization:create_pnl_attribution_stacked_chart:72 - Creating PnL attribution stacked chart grouped by portfolio
2025-06-18 00:19:18 | INFO     | src.visualization:create_pnl_attribution_stacked_chart:146 - PnL attribution stacked chart saved to data/output/charts/pnl_attribution_stacked_portfolio_20250618_001918.html
2025-06-18 00:19:18 | INFO     | src.visualization:create_greeks_heatmap:162 - Creating delta heatmap
2025-06-18 00:19:18 | INFO     | src.visualization:create_greeks_heatmap:225 - Greeks heatmap saved to data/output/charts/greeks_heatmap_delta_20250618_001918.html
2025-06-18 00:19:18 | INFO     | src.visualization:create_greeks_heatmap:162 - Creating gamma heatmap
2025-06-18 00:19:18 | INFO     | src.visualization:create_greeks_heatmap:225 - Greeks heatmap saved to data/output/charts/greeks_heatmap_gamma_20250618_001918.html
2025-06-18 00:19:18 | INFO     | src.visualization:create_portfolio_risk_dashboard:242 - Creating portfolio risk dashboard
2025-06-18 00:19:18 | INFO     | src.visualization:create_portfolio_risk_dashboard:369 - Portfolio risk dashboard saved to data/output/charts/portfolio_risk_dashboard_20250618_001918.html
2025-06-18 00:19:18 | INFO     | src.visualization:create_position_direction_comparison:386 - Creating position direction comparison chart
2025-06-18 00:19:18 | INFO     | src.visualization:create_position_direction_comparison:537 - Position direction comparison chart saved to data/output/charts/position_direction_comparison_20250618_001918.html
2025-06-18 00:19:18 | INFO     | src.visualization:generate_all_charts:585 - Generated 6 charts successfully
2025-06-18 00:19:18 | INFO     | __main__:run_visualization_analysis:368 - Creating visualization report
2025-06-18 00:19:18 | INFO     | src.visualization:create_summary_report:605 - Creating visualization summary report
2025-06-18 00:19:18 | INFO     | src.visualization:create_summary_report:692 - Visualization report saved to data/output/charts/visualization_report_20250618_001918.md
2025-06-18 00:19:18 | INFO     | __main__:run_visualization_analysis:386 - Visualization analysis completed successfully
2025-06-18 00:27:31 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-06-18 00:27:31 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-18 00:27:31 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-18 00:27:31 | INFO     | src.data_processor:load_consolidated_data:137 - Loaded 34 consolidated option records
2025-06-18 00:27:31 | INFO     | src.data_processor:merge_data:188 - Processed consolidated data: 34 records
2025-06-18 00:27:31 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-18 00:27:31 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-18 00:27:31 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-18 00:27:31 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-18 00:27:31 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for cu2505: -0.0662
2025-06-18 00:27:31 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for eb2505: -0.0576
2025-06-18 00:27:31 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for ag2505: -0.0924
2025-06-18 00:27:31 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for sc2505: -0.0589
2025-06-18 00:27:31 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for TA505: -0.0525
2025-06-18 00:27:31 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for ru2505: -0.0433
2025-06-18 00:27:31 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for RM505: 0.0077
2025-06-18 00:27:31 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for PF505: -0.0506
2025-06-18 00:27:31 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for m2505: 0.0192
2025-06-18 00:27:31 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for eg2505: -0.0516
2025-06-18 00:27:31 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for al2505: -0.0695
2025-06-18 00:27:31 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for MA505: -0.0628
2025-06-18 00:27:31 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for OI505: -0.0227
2025-06-18 00:27:31 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for SR505: -0.0225
2025-06-18 00:27:31 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for UR505: -0.0243
2025-06-18 00:27:31 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for i2505: -0.0380
2025-06-18 00:27:31 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for v2505: -0.0333
2025-06-18 00:27:31 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for pp2505: -0.0201
2025-06-18 00:27:31 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for cu2505: 0.4894 (from 0.1651 to 0.6546)
2025-06-18 00:27:31 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for eb2505: 0.0444 (from 0.2577 to 0.3021)
2025-06-18 00:27:31 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for ag2505: 0.5867 (from 0.2232 to 0.8099)
2025-06-18 00:27:31 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for sc2505: 0.9278 (from 0.3722 to 1.3000)
2025-06-18 00:27:31 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for TA505: 0.3711 (from 0.1990 to 0.5701)
2025-06-18 00:27:31 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for ru2505: 0.2573 (from 0.1753 to 0.4326)
2025-06-18 00:27:31 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for RM505: 0.5514 (from 0.0001 to 0.5515)
2025-06-18 00:27:31 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for PF505: -0.1804 (from 0.1805 to 0.0001)
2025-06-18 00:27:31 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for m2505: 0.1201 (from 0.0001 to 0.1202)
2025-06-18 00:27:31 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for eg2505: -0.1575 (from 0.1576 to 0.0001)
2025-06-18 00:27:31 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for al2505: -0.1141 (from 0.1142 to 0.0001)
2025-06-18 00:27:31 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for MA505: -0.0549 (from 0.2314 to 0.1764)
2025-06-18 00:27:31 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for OI505: -0.0350 (from 0.2422 to 0.2072)
2025-06-18 00:27:31 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for SR505: -0.1492 (from 0.1493 to 0.0001)
2025-06-18 00:27:31 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for UR505: 0.2066 (from 0.2654 to 0.4721)
2025-06-18 00:27:31 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for i2505: -0.0425 (from 0.2419 to 0.1993)
2025-06-18 00:27:31 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for v2505: 0.3210 (from 0.1556 to 0.4766)
2025-06-18 00:27:31 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for pp2505: 0.0070 (from 0.1016 to 0.1086)
2025-06-18 00:27:31 | INFO     | src.pnl_attribution:_calculate_vega_pnl:214 - Vega PnL计算完成: 总和=-24092.17, 最大绝对值=6347.52
2025-06-18 00:27:31 | WARNING  | src.pnl_attribution:_calculate_vega_pnl:220 - 发现4个异常大的Vega PnL值
2025-06-18 00:27:31 | INFO     | src.pnl_attribution:_calculate_actual_pnl:374 - Calculating actual PnL from actual option premium changes
2025-06-18 00:27:31 | INFO     | src.pnl_attribution:_calculate_actual_pnl:378 - Using total_option_return as actual PnL baseline
2025-06-18 00:27:31 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-06-18 00:27:31 | INFO     | __main__:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-18 00:27:31 | INFO     | __main__:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-18 00:27:31 | INFO     | src.data_processor:prepare_database_export_data:242 - Prepared database export data: 34 records, 50 columns
2025-06-18 00:27:31 | INFO     | __main__:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-18 00:27:31 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_detail_20250618_002731.xlsx
2025-06-18 00:27:31 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_summary_20250618_002731.xlsx
2025-06-18 00:27:31 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/database_export.xlsx
2025-06-18 00:27:31 | INFO     | __main__:run_full_analysis:149 - Step 7: Exporting to database (mode: full)
2025-06-18 00:27:31 | ERROR    | src.database_exporter:connect:78 - Failed to connect to database: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost' ([Errno 61] Connection refused)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-18 00:27:31 | INFO     | src.database_exporter:close:462 - Database connection closed
2025-06-18 00:27:31 | INFO     | __main__:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-06-18 00:31:19 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-06-18 00:31:19 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-18 00:31:19 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-18 00:31:19 | INFO     | src.data_processor:load_consolidated_data:137 - Loaded 34 consolidated option records
2025-06-18 00:31:19 | INFO     | src.data_processor:merge_data:188 - Processed consolidated data: 34 records
2025-06-18 00:31:19 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-18 00:31:19 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-18 00:31:19 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-18 00:31:19 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-18 00:31:19 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for cu2505: -0.0662
2025-06-18 00:31:19 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for eb2505: -0.0576
2025-06-18 00:31:19 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for ag2505: -0.0924
2025-06-18 00:31:19 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for sc2505: -0.0589
2025-06-18 00:31:19 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for TA505: -0.0525
2025-06-18 00:31:19 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for ru2505: -0.0433
2025-06-18 00:31:19 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for RM505: 0.0077
2025-06-18 00:31:19 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for PF505: -0.0506
2025-06-18 00:31:19 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for m2505: 0.0192
2025-06-18 00:31:19 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for eg2505: -0.0516
2025-06-18 00:31:19 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for al2505: -0.0695
2025-06-18 00:31:19 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for MA505: -0.0628
2025-06-18 00:31:19 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for OI505: -0.0227
2025-06-18 00:31:19 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for SR505: -0.0225
2025-06-18 00:31:19 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for UR505: -0.0243
2025-06-18 00:31:19 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for i2505: -0.0380
2025-06-18 00:31:19 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for v2505: -0.0333
2025-06-18 00:31:19 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for pp2505: -0.0201
2025-06-18 00:31:19 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for cu2505: 0.4894 (from 0.1651 to 0.6546)
2025-06-18 00:31:19 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for eb2505: 0.0444 (from 0.2577 to 0.3021)
2025-06-18 00:31:19 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for ag2505: 0.5867 (from 0.2232 to 0.8099)
2025-06-18 00:31:19 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for sc2505: 0.9278 (from 0.3722 to 1.3000)
2025-06-18 00:31:19 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for TA505: 0.3711 (from 0.1990 to 0.5701)
2025-06-18 00:31:19 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for ru2505: 0.2573 (from 0.1753 to 0.4326)
2025-06-18 00:31:19 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for RM505: 0.5514 (from 0.0001 to 0.5515)
2025-06-18 00:31:19 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for PF505: -0.1804 (from 0.1805 to 0.0001)
2025-06-18 00:31:19 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for m2505: 0.1201 (from 0.0001 to 0.1202)
2025-06-18 00:31:19 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for eg2505: -0.1575 (from 0.1576 to 0.0001)
2025-06-18 00:31:19 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for al2505: -0.1141 (from 0.1142 to 0.0001)
2025-06-18 00:31:19 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for MA505: -0.0549 (from 0.2314 to 0.1764)
2025-06-18 00:31:19 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for OI505: -0.0350 (from 0.2422 to 0.2072)
2025-06-18 00:31:19 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for SR505: -0.1492 (from 0.1493 to 0.0001)
2025-06-18 00:31:19 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for UR505: 0.2066 (from 0.2654 to 0.4721)
2025-06-18 00:31:19 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for i2505: -0.0425 (from 0.2419 to 0.1993)
2025-06-18 00:31:19 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for v2505: 0.3210 (from 0.1556 to 0.4766)
2025-06-18 00:31:19 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for pp2505: 0.0070 (from 0.1016 to 0.1086)
2025-06-18 00:31:19 | INFO     | src.pnl_attribution:_calculate_vega_pnl:214 - Vega PnL计算完成: 总和=-24092.17, 最大绝对值=6347.52
2025-06-18 00:31:19 | WARNING  | src.pnl_attribution:_calculate_vega_pnl:220 - 发现4个异常大的Vega PnL值
2025-06-18 00:31:19 | INFO     | src.pnl_attribution:_calculate_actual_pnl:374 - Calculating actual PnL from actual option premium changes
2025-06-18 00:31:19 | INFO     | src.pnl_attribution:_calculate_actual_pnl:378 - Using total_option_return as actual PnL baseline
2025-06-18 00:31:19 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-06-18 00:31:19 | INFO     | __main__:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-18 00:31:19 | INFO     | __main__:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-18 00:31:19 | INFO     | src.data_processor:prepare_database_export_data:242 - Prepared database export data: 34 records, 50 columns
2025-06-18 00:31:19 | INFO     | __main__:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-18 00:31:19 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_detail_20250618_003119.xlsx
2025-06-18 00:31:19 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_summary_20250618_003119.xlsx
2025-06-18 00:31:20 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/database_export.xlsx
2025-06-18 00:31:20 | INFO     | __main__:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-06-18 00:31:43 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-06-18 00:31:43 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-18 00:31:43 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-18 00:31:44 | INFO     | src.data_processor:load_consolidated_data:137 - Loaded 34 consolidated option records
2025-06-18 00:31:44 | INFO     | src.data_processor:merge_data:188 - Processed consolidated data: 34 records
2025-06-18 00:31:44 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-18 00:31:44 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-18 00:31:44 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-18 00:31:44 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-18 00:31:44 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for cu2505: -0.0662
2025-06-18 00:31:44 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for eb2505: -0.0576
2025-06-18 00:31:44 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for ag2505: -0.0924
2025-06-18 00:31:44 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for sc2505: -0.0589
2025-06-18 00:31:44 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for TA505: -0.0525
2025-06-18 00:31:44 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for ru2505: -0.0433
2025-06-18 00:31:44 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for RM505: 0.0077
2025-06-18 00:31:44 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for PF505: -0.0506
2025-06-18 00:31:44 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for m2505: 0.0192
2025-06-18 00:31:44 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for eg2505: -0.0516
2025-06-18 00:31:44 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for al2505: -0.0695
2025-06-18 00:31:44 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for MA505: -0.0628
2025-06-18 00:31:44 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for OI505: -0.0227
2025-06-18 00:31:44 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for SR505: -0.0225
2025-06-18 00:31:44 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for UR505: -0.0243
2025-06-18 00:31:44 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for i2505: -0.0380
2025-06-18 00:31:44 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for v2505: -0.0333
2025-06-18 00:31:44 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:485 - Calculated actual price change for pp2505: -0.0201
2025-06-18 00:31:44 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for cu2505: 0.4894 (from 0.1651 to 0.6546)
2025-06-18 00:31:44 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for eb2505: 0.0444 (from 0.2577 to 0.3021)
2025-06-18 00:31:44 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for ag2505: 0.5867 (from 0.2232 to 0.8099)
2025-06-18 00:31:44 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for sc2505: 0.9278 (from 0.3722 to 1.3000)
2025-06-18 00:31:44 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for TA505: 0.3711 (from 0.1990 to 0.5701)
2025-06-18 00:31:44 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for ru2505: 0.2573 (from 0.1753 to 0.4326)
2025-06-18 00:31:44 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for RM505: 0.5514 (from 0.0001 to 0.5515)
2025-06-18 00:31:44 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for PF505: -0.1804 (from 0.1805 to 0.0001)
2025-06-18 00:31:44 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for m2505: 0.1201 (from 0.0001 to 0.1202)
2025-06-18 00:31:44 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for eg2505: -0.1575 (from 0.1576 to 0.0001)
2025-06-18 00:31:44 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for al2505: -0.1141 (from 0.1142 to 0.0001)
2025-06-18 00:31:44 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for MA505: -0.0549 (from 0.2314 to 0.1764)
2025-06-18 00:31:44 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for OI505: -0.0350 (from 0.2422 to 0.2072)
2025-06-18 00:31:44 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for SR505: -0.1492 (from 0.1493 to 0.0001)
2025-06-18 00:31:44 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for UR505: 0.2066 (from 0.2654 to 0.4721)
2025-06-18 00:31:44 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for i2505: -0.0425 (from 0.2419 to 0.1993)
2025-06-18 00:31:44 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for v2505: 0.3210 (from 0.1556 to 0.4766)
2025-06-18 00:31:44 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:553 - Calculated actual IV change for pp2505: 0.0070 (from 0.1016 to 0.1086)
2025-06-18 00:31:44 | INFO     | src.pnl_attribution:_calculate_vega_pnl:214 - Vega PnL计算完成: 总和=-24092.17, 最大绝对值=6347.52
2025-06-18 00:31:44 | WARNING  | src.pnl_attribution:_calculate_vega_pnl:220 - 发现4个异常大的Vega PnL值
2025-06-18 00:31:44 | INFO     | src.pnl_attribution:_calculate_actual_pnl:374 - Calculating actual PnL from actual option premium changes
2025-06-18 00:31:44 | INFO     | src.pnl_attribution:_calculate_actual_pnl:378 - Using total_option_return as actual PnL baseline
2025-06-18 00:31:44 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-06-18 00:31:44 | INFO     | __main__:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-18 00:31:44 | INFO     | __main__:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-18 00:31:44 | INFO     | src.data_processor:prepare_database_export_data:242 - Prepared database export data: 34 records, 50 columns
2025-06-18 00:31:44 | INFO     | __main__:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-18 00:31:44 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_detail_20250618_003144.xlsx
2025-06-18 00:31:44 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_summary_20250618_003144.xlsx
2025-06-18 00:31:44 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/database_export.xlsx
2025-06-18 00:31:44 | INFO     | __main__:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-06-18 00:31:57 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-06-18 00:31:57 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-18 00:31:57 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-18 00:31:57 | INFO     | src.data_processor:load_consolidated_data:137 - Loaded 34 consolidated option records
2025-06-18 00:31:57 | INFO     | src.data_processor:merge_data:188 - Processed consolidated data: 34 records
2025-06-18 00:31:57 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-18 00:31:57 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-18 00:31:57 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-18 00:31:57 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-18 00:31:57 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for cu2505: -0.0662
2025-06-18 00:31:57 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for eb2505: -0.0576
2025-06-18 00:31:57 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for ag2505: -0.0924
2025-06-18 00:31:57 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for sc2505: -0.0589
2025-06-18 00:31:57 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for TA505: -0.0525
2025-06-18 00:31:57 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for ru2505: -0.0433
2025-06-18 00:31:57 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for RM505: 0.0077
2025-06-18 00:31:57 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for PF505: -0.0506
2025-06-18 00:31:57 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for m2505: 0.0192
2025-06-18 00:31:57 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for eg2505: -0.0516
2025-06-18 00:31:57 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for al2505: -0.0695
2025-06-18 00:31:57 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for MA505: -0.0628
2025-06-18 00:31:57 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for OI505: -0.0227
2025-06-18 00:31:57 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for SR505: -0.0225
2025-06-18 00:31:57 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for UR505: -0.0243
2025-06-18 00:31:57 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for i2505: -0.0380
2025-06-18 00:31:57 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for v2505: -0.0333
2025-06-18 00:31:57 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:473 - Calculated actual price change for pp2505: -0.0201
2025-06-18 00:31:57 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for cu2505: 0.4894 (from 0.1651 to 0.6546)
2025-06-18 00:31:57 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for eb2505: 0.0444 (from 0.2577 to 0.3021)
2025-06-18 00:31:57 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for ag2505: 0.5867 (from 0.2232 to 0.8099)
2025-06-18 00:31:57 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for sc2505: 0.9278 (from 0.3722 to 1.3000)
2025-06-18 00:31:57 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for TA505: 0.3711 (from 0.1990 to 0.5701)
2025-06-18 00:31:57 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for ru2505: 0.2573 (from 0.1753 to 0.4326)
2025-06-18 00:31:57 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for RM505: 0.5514 (from 0.0001 to 0.5515)
2025-06-18 00:31:57 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for PF505: -0.1804 (from 0.1805 to 0.0001)
2025-06-18 00:31:57 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for m2505: 0.1201 (from 0.0001 to 0.1202)
2025-06-18 00:31:57 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for eg2505: -0.1575 (from 0.1576 to 0.0001)
2025-06-18 00:31:57 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for al2505: -0.1141 (from 0.1142 to 0.0001)
2025-06-18 00:31:57 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for MA505: -0.0549 (from 0.2314 to 0.1764)
2025-06-18 00:31:57 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for OI505: -0.0350 (from 0.2422 to 0.2072)
2025-06-18 00:31:57 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for SR505: -0.1492 (from 0.1493 to 0.0001)
2025-06-18 00:31:57 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for UR505: 0.2066 (from 0.2654 to 0.4721)
2025-06-18 00:31:57 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for i2505: -0.0425 (from 0.2419 to 0.1993)
2025-06-18 00:31:57 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for v2505: 0.3210 (from 0.1556 to 0.4766)
2025-06-18 00:31:57 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:541 - Calculated actual IV change for pp2505: 0.0070 (from 0.1016 to 0.1086)
2025-06-18 00:31:57 | INFO     | src.pnl_attribution:_calculate_actual_pnl:362 - Calculating actual PnL from actual option premium changes
2025-06-18 00:31:57 | INFO     | src.pnl_attribution:_calculate_actual_pnl:366 - Using total_option_return as actual PnL baseline
2025-06-18 00:31:57 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-06-18 00:31:57 | INFO     | __main__:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-18 00:31:57 | INFO     | __main__:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-18 00:31:57 | INFO     | src.data_processor:prepare_database_export_data:242 - Prepared database export data: 34 records, 50 columns
2025-06-18 00:31:57 | INFO     | __main__:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-18 00:31:57 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_detail_20250618_003157.xlsx
2025-06-18 00:31:57 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_summary_20250618_003157.xlsx
2025-06-18 00:31:57 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/database_export.xlsx
2025-06-18 00:31:57 | INFO     | __main__:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-06-18 00:41:13 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-06-18 00:41:13 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-18 00:41:13 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-18 00:41:13 | INFO     | src.data_processor:load_consolidated_data:137 - Loaded 34 consolidated option records
2025-06-18 00:41:13 | INFO     | src.data_processor:merge_data:188 - Processed consolidated data: 34 records
2025-06-18 00:41:13 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-18 00:41:13 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-18 00:41:13 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-18 00:41:13 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-18 00:41:13 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:486 - Calculated actual price change for cu2505: -0.0136
2025-06-18 00:41:13 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:486 - Calculated actual price change for eb2505: 0.0041
2025-06-18 00:41:13 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:486 - Calculated actual price change for ag2505: 0.0168
2025-06-18 00:41:13 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:486 - Calculated actual price change for sc2505: 0.0030
2025-06-18 00:41:13 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:486 - Calculated actual price change for TA505: -0.0081
2025-06-18 00:41:13 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:486 - Calculated actual price change for ru2505: -0.0180
2025-06-18 00:41:13 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:486 - Calculated actual price change for RM505: -0.0127
2025-06-18 00:41:13 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:486 - Calculated actual price change for PF505: -0.0003
2025-06-18 00:41:13 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:486 - Calculated actual price change for m2505: -0.0046
2025-06-18 00:41:13 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:486 - Calculated actual price change for eg2505: -0.0087
2025-06-18 00:41:13 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:486 - Calculated actual price change for al2505: -0.0113
2025-06-18 00:41:13 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:486 - Calculated actual price change for MA505: -0.0086
2025-06-18 00:41:13 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:486 - Calculated actual price change for OI505: 0.0104
2025-06-18 00:41:13 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:486 - Calculated actual price change for SR505: 0.0020
2025-06-18 00:41:13 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:486 - Calculated actual price change for UR505: 0.0011
2025-06-18 00:41:13 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:486 - Calculated actual price change for i2505: -0.0044
2025-06-18 00:41:13 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:486 - Calculated actual price change for v2505: -0.0021
2025-06-18 00:41:13 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:486 - Calculated actual price change for pp2505: -0.0011
2025-06-18 00:41:13 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:554 - Calculated actual IV change for cu2505: -0.0426 (from 0.1748 to 0.1322)
2025-06-18 00:41:13 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:554 - Calculated actual IV change for eb2505: -0.0358 (from 0.2031 to 0.1673)
2025-06-18 00:41:13 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:554 - Calculated actual IV change for ag2505: -0.0118 (from 0.1843 to 0.1725)
2025-06-18 00:41:13 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:554 - Calculated actual IV change for sc2505: -0.0456 (from 0.2569 to 0.2114)
2025-06-18 00:41:13 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:554 - Calculated actual IV change for TA505: -0.0432 (from 0.1632 to 0.1199)
2025-06-18 00:41:13 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:554 - Calculated actual IV change for ru2505: -0.0275 (from 0.1768 to 0.1493)
2025-06-18 00:41:13 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:554 - Calculated actual IV change for RM505: 0.2389 (from 0.0001 to 0.2390)
2025-06-18 00:41:13 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:554 - Calculated actual IV change for PF505: -0.0227 (from 0.1207 to 0.0980)
2025-06-18 00:41:13 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:554 - Calculated actual IV change for m2505: 0.1596 (from 0.0001 to 0.1597)
2025-06-18 00:41:13 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:554 - Calculated actual IV change for eg2505: -0.0260 (from 0.1279 to 0.1020)
2025-06-18 00:41:13 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:554 - Calculated actual IV change for al2505: -0.0221 (from 0.1061 to 0.0840)
2025-06-18 00:41:13 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:554 - Calculated actual IV change for MA505: -0.0365 (from 0.1910 to 0.1545)
2025-06-18 00:41:13 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:554 - Calculated actual IV change for OI505: -0.0367 (from 0.1823 to 0.1456)
2025-06-18 00:41:13 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:554 - Calculated actual IV change for SR505: -0.0244 (from 0.1069 to 0.0824)
2025-06-18 00:41:13 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:554 - Calculated actual IV change for UR505: -0.0392 (from 0.2048 to 0.1656)
2025-06-18 00:41:13 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:554 - Calculated actual IV change for i2505: -0.0439 (from 0.2290 to 0.1851)
2025-06-18 00:41:13 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:554 - Calculated actual IV change for v2505: -0.0235 (from 0.1349 to 0.1114)
2025-06-18 00:41:13 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:554 - Calculated actual IV change for pp2505: -0.0025 (from 0.0818 to 0.0793)
2025-06-18 00:41:13 | INFO     | src.pnl_attribution:_calculate_total_pnl:366 - PnL归因汇总 - 主要项: -6390.12, 交叉项: -115458.10, 总计: -121848.22
2025-06-18 00:41:13 | INFO     | src.pnl_attribution:_calculate_actual_pnl:375 - Calculating actual PnL from actual option premium changes
2025-06-18 00:41:13 | INFO     | src.pnl_attribution:_calculate_actual_pnl:379 - Using total_option_return as actual PnL baseline
2025-06-18 00:41:13 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-06-18 00:41:13 | INFO     | __main__:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-18 00:41:13 | INFO     | __main__:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-18 00:41:13 | INFO     | src.data_processor:prepare_database_export_data:242 - Prepared database export data: 34 records, 50 columns
2025-06-18 00:41:13 | INFO     | __main__:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-18 00:41:13 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_detail_20250618_004113.xlsx
2025-06-18 00:41:13 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_summary_20250618_004113.xlsx
2025-06-18 00:41:13 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/database_export.xlsx
2025-06-18 00:41:13 | INFO     | __main__:run_full_analysis:149 - Step 7: Exporting to database (mode: full)
2025-06-18 00:41:13 | ERROR    | src.database_exporter:connect:78 - Failed to connect to database: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost' ([Errno 61] Connection refused)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-18 00:41:13 | INFO     | src.database_exporter:close:462 - Database connection closed
2025-06-18 00:41:13 | INFO     | __main__:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-06-18 00:44:12 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-06-18 00:44:12 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-18 00:44:12 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-18 00:44:12 | INFO     | src.data_processor:load_consolidated_data:137 - Loaded 34 consolidated option records
2025-06-18 00:44:12 | INFO     | src.data_processor:merge_data:188 - Processed consolidated data: 34 records
2025-06-18 00:44:12 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-18 00:44:12 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-18 00:44:12 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-18 00:44:12 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-18 00:44:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:500 - Calculated actual price change for cu2505: -0.0136
2025-06-18 00:44:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:500 - Calculated actual price change for eb2505: 0.0041
2025-06-18 00:44:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:500 - Calculated actual price change for ag2505: 0.0168
2025-06-18 00:44:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:500 - Calculated actual price change for sc2505: 0.0030
2025-06-18 00:44:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:500 - Calculated actual price change for TA505: -0.0081
2025-06-18 00:44:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:500 - Calculated actual price change for ru2505: -0.0180
2025-06-18 00:44:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:500 - Calculated actual price change for RM505: -0.0127
2025-06-18 00:44:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:500 - Calculated actual price change for PF505: -0.0003
2025-06-18 00:44:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:500 - Calculated actual price change for m2505: -0.0046
2025-06-18 00:44:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:500 - Calculated actual price change for eg2505: -0.0087
2025-06-18 00:44:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:500 - Calculated actual price change for al2505: -0.0113
2025-06-18 00:44:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:500 - Calculated actual price change for MA505: -0.0086
2025-06-18 00:44:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:500 - Calculated actual price change for OI505: 0.0104
2025-06-18 00:44:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:500 - Calculated actual price change for SR505: 0.0020
2025-06-18 00:44:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:500 - Calculated actual price change for UR505: 0.0011
2025-06-18 00:44:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:500 - Calculated actual price change for i2505: -0.0044
2025-06-18 00:44:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:500 - Calculated actual price change for v2505: -0.0021
2025-06-18 00:44:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:500 - Calculated actual price change for pp2505: -0.0011
2025-06-18 00:44:13 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:568 - Calculated actual IV change for cu2505: -0.0426 (from 0.1748 to 0.1322)
2025-06-18 00:44:13 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:568 - Calculated actual IV change for eb2505: -0.0358 (from 0.2031 to 0.1673)
2025-06-18 00:44:13 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:568 - Calculated actual IV change for ag2505: -0.0118 (from 0.1843 to 0.1725)
2025-06-18 00:44:13 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:568 - Calculated actual IV change for sc2505: -0.0456 (from 0.2569 to 0.2114)
2025-06-18 00:44:13 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:568 - Calculated actual IV change for TA505: -0.0432 (from 0.1632 to 0.1199)
2025-06-18 00:44:13 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:568 - Calculated actual IV change for ru2505: -0.0275 (from 0.1768 to 0.1493)
2025-06-18 00:44:13 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:568 - Calculated actual IV change for RM505: 0.2389 (from 0.0001 to 0.2390)
2025-06-18 00:44:13 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:568 - Calculated actual IV change for PF505: -0.0227 (from 0.1207 to 0.0980)
2025-06-18 00:44:13 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:568 - Calculated actual IV change for m2505: 0.1596 (from 0.0001 to 0.1597)
2025-06-18 00:44:13 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:568 - Calculated actual IV change for eg2505: -0.0260 (from 0.1279 to 0.1020)
2025-06-18 00:44:13 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:568 - Calculated actual IV change for al2505: -0.0221 (from 0.1061 to 0.0840)
2025-06-18 00:44:13 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:568 - Calculated actual IV change for MA505: -0.0365 (from 0.1910 to 0.1545)
2025-06-18 00:44:13 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:568 - Calculated actual IV change for OI505: -0.0367 (from 0.1823 to 0.1456)
2025-06-18 00:44:13 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:568 - Calculated actual IV change for SR505: -0.0244 (from 0.1069 to 0.0824)
2025-06-18 00:44:13 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:568 - Calculated actual IV change for UR505: -0.0392 (from 0.2048 to 0.1656)
2025-06-18 00:44:13 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:568 - Calculated actual IV change for i2505: -0.0439 (from 0.2290 to 0.1851)
2025-06-18 00:44:13 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:568 - Calculated actual IV change for v2505: -0.0235 (from 0.1349 to 0.1114)
2025-06-18 00:44:13 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:568 - Calculated actual IV change for pp2505: -0.0025 (from 0.0818 to 0.0793)
2025-06-18 00:44:13 | INFO     | src.pnl_attribution:_calculate_total_pnl:380 - PnL归因汇总 - 主要项: -6390.12, 交叉项: -26513.16, 总计: -32903.28
2025-06-18 00:44:13 | INFO     | src.pnl_attribution:_calculate_actual_pnl:389 - Calculating actual PnL from actual option premium changes
2025-06-18 00:44:13 | INFO     | src.pnl_attribution:_calculate_actual_pnl:393 - Using total_option_return as actual PnL baseline
2025-06-18 00:44:13 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-06-18 00:44:13 | INFO     | __main__:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-18 00:44:13 | INFO     | __main__:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-18 00:44:13 | INFO     | src.data_processor:prepare_database_export_data:242 - Prepared database export data: 34 records, 50 columns
2025-06-18 00:44:13 | INFO     | __main__:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-18 00:44:13 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_detail_20250618_004413.xlsx
2025-06-18 00:44:13 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_summary_20250618_004413.xlsx
2025-06-18 00:44:13 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/database_export.xlsx
2025-06-18 00:44:13 | INFO     | __main__:run_full_analysis:149 - Step 7: Exporting to database (mode: full)
2025-06-18 00:44:13 | ERROR    | src.database_exporter:connect:78 - Failed to connect to database: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost' ([Errno 61] Connection refused)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-18 00:44:13 | INFO     | src.database_exporter:close:462 - Database connection closed
2025-06-18 00:44:13 | INFO     | __main__:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-06-18 00:45:52 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-06-18 00:45:52 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-18 00:45:52 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-18 00:45:53 | INFO     | src.data_processor:load_consolidated_data:137 - Loaded 34 consolidated option records
2025-06-18 00:45:53 | INFO     | src.data_processor:merge_data:188 - Processed consolidated data: 34 records
2025-06-18 00:45:53 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-18 00:45:53 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-18 00:45:53 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-18 00:45:53 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-18 00:45:53 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:500 - Calculated actual price change for cu2505: -0.0136
2025-06-18 00:45:53 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:500 - Calculated actual price change for eb2505: 0.0041
2025-06-18 00:45:53 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:500 - Calculated actual price change for ag2505: 0.0168
2025-06-18 00:45:53 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:500 - Calculated actual price change for sc2505: 0.0030
2025-06-18 00:45:53 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:500 - Calculated actual price change for TA505: -0.0081
2025-06-18 00:45:53 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:500 - Calculated actual price change for ru2505: -0.0180
2025-06-18 00:45:53 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:500 - Calculated actual price change for RM505: -0.0127
2025-06-18 00:45:53 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:500 - Calculated actual price change for PF505: -0.0003
2025-06-18 00:45:53 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:500 - Calculated actual price change for m2505: -0.0046
2025-06-18 00:45:53 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:500 - Calculated actual price change for eg2505: -0.0087
2025-06-18 00:45:53 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:500 - Calculated actual price change for al2505: -0.0113
2025-06-18 00:45:53 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:500 - Calculated actual price change for MA505: -0.0086
2025-06-18 00:45:53 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:500 - Calculated actual price change for OI505: 0.0104
2025-06-18 00:45:53 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:500 - Calculated actual price change for SR505: 0.0020
2025-06-18 00:45:53 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:500 - Calculated actual price change for UR505: 0.0011
2025-06-18 00:45:53 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:500 - Calculated actual price change for i2505: -0.0044
2025-06-18 00:45:53 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:500 - Calculated actual price change for v2505: -0.0021
2025-06-18 00:45:53 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:500 - Calculated actual price change for pp2505: -0.0011
2025-06-18 00:45:53 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:568 - Calculated actual IV change for cu2505: -0.0426 (from 0.1748 to 0.1322)
2025-06-18 00:45:53 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:568 - Calculated actual IV change for eb2505: -0.0358 (from 0.2031 to 0.1673)
2025-06-18 00:45:53 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:568 - Calculated actual IV change for ag2505: -0.0118 (from 0.1843 to 0.1725)
2025-06-18 00:45:53 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:568 - Calculated actual IV change for sc2505: -0.0456 (from 0.2569 to 0.2114)
2025-06-18 00:45:53 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:568 - Calculated actual IV change for TA505: -0.0432 (from 0.1632 to 0.1199)
2025-06-18 00:45:53 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:568 - Calculated actual IV change for ru2505: -0.0275 (from 0.1768 to 0.1493)
2025-06-18 00:45:53 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:568 - Calculated actual IV change for RM505: 0.2389 (from 0.0001 to 0.2390)
2025-06-18 00:45:53 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:568 - Calculated actual IV change for PF505: -0.0227 (from 0.1207 to 0.0980)
2025-06-18 00:45:53 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:568 - Calculated actual IV change for m2505: 0.1596 (from 0.0001 to 0.1597)
2025-06-18 00:45:53 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:568 - Calculated actual IV change for eg2505: -0.0260 (from 0.1279 to 0.1020)
2025-06-18 00:45:53 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:568 - Calculated actual IV change for al2505: -0.0221 (from 0.1061 to 0.0840)
2025-06-18 00:45:53 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:568 - Calculated actual IV change for MA505: -0.0365 (from 0.1910 to 0.1545)
2025-06-18 00:45:53 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:568 - Calculated actual IV change for OI505: -0.0367 (from 0.1823 to 0.1456)
2025-06-18 00:45:53 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:568 - Calculated actual IV change for SR505: -0.0244 (from 0.1069 to 0.0824)
2025-06-18 00:45:53 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:568 - Calculated actual IV change for UR505: -0.0392 (from 0.2048 to 0.1656)
2025-06-18 00:45:53 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:568 - Calculated actual IV change for i2505: -0.0439 (from 0.2290 to 0.1851)
2025-06-18 00:45:53 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:568 - Calculated actual IV change for v2505: -0.0235 (from 0.1349 to 0.1114)
2025-06-18 00:45:53 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:568 - Calculated actual IV change for pp2505: -0.0025 (from 0.0818 to 0.0793)
2025-06-18 00:45:53 | INFO     | src.pnl_attribution:_calculate_total_pnl:380 - PnL归因汇总 - 主要项: -6390.12, 交叉项: -26513.16, 总计: -32903.28
2025-06-18 00:45:53 | INFO     | src.pnl_attribution:_calculate_actual_pnl:389 - Calculating actual PnL from actual option premium changes
2025-06-18 00:45:53 | INFO     | src.pnl_attribution:_calculate_actual_pnl:393 - Using total_option_return as actual PnL baseline
2025-06-18 00:45:53 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-06-18 00:45:53 | INFO     | __main__:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-18 00:45:53 | INFO     | __main__:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-18 00:45:53 | INFO     | src.data_processor:prepare_database_export_data:242 - Prepared database export data: 34 records, 50 columns
2025-06-18 00:45:53 | INFO     | __main__:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-18 00:45:53 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_detail_20250618_004553.xlsx
2025-06-18 00:45:53 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_summary_20250618_004553.xlsx
2025-06-18 00:45:53 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/database_export.xlsx
2025-06-18 00:45:53 | INFO     | __main__:run_full_analysis:149 - Step 7: Exporting to database (mode: full)
2025-06-18 00:45:53 | ERROR    | src.database_exporter:connect:78 - Failed to connect to database: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost' ([Errno 61] Connection refused)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-18 00:45:53 | INFO     | src.database_exporter:close:462 - Database connection closed
2025-06-18 00:45:53 | INFO     | __main__:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-06-18 00:53:27 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-06-18 00:53:27 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-18 00:53:27 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-18 00:53:27 | INFO     | src.data_processor:load_consolidated_data:137 - Loaded 34 consolidated option records
2025-06-18 00:53:27 | INFO     | src.data_processor:merge_data:188 - Processed consolidated data: 34 records
2025-06-18 00:53:27 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-18 00:53:27 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-18 00:53:27 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-18 00:53:27 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-18 00:53:27 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:489 - Calculated actual price change for cu2505: -0.0136
2025-06-18 00:53:27 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:489 - Calculated actual price change for eb2505: 0.0041
2025-06-18 00:53:27 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:489 - Calculated actual price change for ag2505: 0.0168
2025-06-18 00:53:27 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:489 - Calculated actual price change for sc2505: 0.0030
2025-06-18 00:53:27 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:489 - Calculated actual price change for TA505: -0.0081
2025-06-18 00:53:27 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:489 - Calculated actual price change for ru2505: -0.0180
2025-06-18 00:53:27 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:489 - Calculated actual price change for RM505: -0.0127
2025-06-18 00:53:27 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:489 - Calculated actual price change for PF505: -0.0003
2025-06-18 00:53:27 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:489 - Calculated actual price change for m2505: -0.0046
2025-06-18 00:53:27 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:489 - Calculated actual price change for eg2505: -0.0087
2025-06-18 00:53:27 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:489 - Calculated actual price change for al2505: -0.0113
2025-06-18 00:53:27 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:489 - Calculated actual price change for MA505: -0.0086
2025-06-18 00:53:27 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:489 - Calculated actual price change for OI505: 0.0104
2025-06-18 00:53:27 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:489 - Calculated actual price change for SR505: 0.0020
2025-06-18 00:53:27 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:489 - Calculated actual price change for UR505: 0.0011
2025-06-18 00:53:27 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:489 - Calculated actual price change for i2505: -0.0044
2025-06-18 00:53:27 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:489 - Calculated actual price change for v2505: -0.0021
2025-06-18 00:53:27 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:489 - Calculated actual price change for pp2505: -0.0011
2025-06-18 00:53:27 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:557 - Calculated actual IV change for cu2505: -0.0426 (from 0.1748 to 0.1322)
2025-06-18 00:53:27 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:557 - Calculated actual IV change for eb2505: -0.0358 (from 0.2031 to 0.1673)
2025-06-18 00:53:27 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:557 - Calculated actual IV change for ag2505: -0.0118 (from 0.1843 to 0.1725)
2025-06-18 00:53:27 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:557 - Calculated actual IV change for sc2505: -0.0456 (from 0.2569 to 0.2114)
2025-06-18 00:53:27 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:557 - Calculated actual IV change for TA505: -0.0432 (from 0.1632 to 0.1199)
2025-06-18 00:53:27 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:557 - Calculated actual IV change for ru2505: -0.0275 (from 0.1768 to 0.1493)
2025-06-18 00:53:27 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:557 - Calculated actual IV change for RM505: 0.2389 (from 0.0001 to 0.2390)
2025-06-18 00:53:27 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:557 - Calculated actual IV change for PF505: -0.0227 (from 0.1207 to 0.0980)
2025-06-18 00:53:27 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:557 - Calculated actual IV change for m2505: 0.1596 (from 0.0001 to 0.1597)
2025-06-18 00:53:27 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:557 - Calculated actual IV change for eg2505: -0.0260 (from 0.1279 to 0.1020)
2025-06-18 00:53:27 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:557 - Calculated actual IV change for al2505: -0.0221 (from 0.1061 to 0.0840)
2025-06-18 00:53:27 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:557 - Calculated actual IV change for MA505: -0.0365 (from 0.1910 to 0.1545)
2025-06-18 00:53:27 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:557 - Calculated actual IV change for OI505: -0.0367 (from 0.1823 to 0.1456)
2025-06-18 00:53:27 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:557 - Calculated actual IV change for SR505: -0.0244 (from 0.1069 to 0.0824)
2025-06-18 00:53:27 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:557 - Calculated actual IV change for UR505: -0.0392 (from 0.2048 to 0.1656)
2025-06-18 00:53:27 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:557 - Calculated actual IV change for i2505: -0.0439 (from 0.2290 to 0.1851)
2025-06-18 00:53:27 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:557 - Calculated actual IV change for v2505: -0.0235 (from 0.1349 to 0.1114)
2025-06-18 00:53:27 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:557 - Calculated actual IV change for pp2505: -0.0025 (from 0.0818 to 0.0793)
2025-06-18 00:53:27 | INFO     | src.pnl_attribution:_calculate_total_pnl:372 - PnL归因汇总 - 主要项: -6390.12, 交叉项: -115458.10, 总计: -121848.22
2025-06-18 00:53:27 | INFO     | src.pnl_attribution:_calculate_actual_pnl:380 - Calculating actual PnL from actual option premium changes per record
2025-06-18 00:53:27 | WARNING  | src.pnl_attribution:_calculate_actual_pnl:387 - 'option_return' column not found. Calculating actual PnL from price differences.
2025-06-18 00:53:27 | ERROR    | __main__:run_full_analysis:170 - PnL attribution analysis failed: name 'new_df' is not defined
2025-06-18 00:53:57 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-06-18 00:53:57 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-18 00:53:57 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-18 00:53:57 | INFO     | src.data_processor:load_consolidated_data:137 - Loaded 34 consolidated option records
2025-06-18 00:53:57 | INFO     | src.data_processor:merge_data:188 - Processed consolidated data: 34 records
2025-06-18 00:53:57 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-18 00:53:57 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-18 00:53:57 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-18 00:53:57 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-18 00:53:57 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:460 - Calculated actual price change for cu2505: -0.0136
2025-06-18 00:53:57 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:460 - Calculated actual price change for eb2505: 0.0041
2025-06-18 00:53:57 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:460 - Calculated actual price change for ag2505: 0.0168
2025-06-18 00:53:57 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:460 - Calculated actual price change for sc2505: 0.0030
2025-06-18 00:53:57 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:460 - Calculated actual price change for TA505: -0.0081
2025-06-18 00:53:57 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:460 - Calculated actual price change for ru2505: -0.0180
2025-06-18 00:53:57 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:460 - Calculated actual price change for RM505: -0.0127
2025-06-18 00:53:57 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:460 - Calculated actual price change for PF505: -0.0003
2025-06-18 00:53:57 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:460 - Calculated actual price change for m2505: -0.0046
2025-06-18 00:53:57 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:460 - Calculated actual price change for eg2505: -0.0087
2025-06-18 00:53:57 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:460 - Calculated actual price change for al2505: -0.0113
2025-06-18 00:53:57 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:460 - Calculated actual price change for MA505: -0.0086
2025-06-18 00:53:57 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:460 - Calculated actual price change for OI505: 0.0104
2025-06-18 00:53:57 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:460 - Calculated actual price change for SR505: 0.0020
2025-06-18 00:53:57 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:460 - Calculated actual price change for UR505: 0.0011
2025-06-18 00:53:57 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:460 - Calculated actual price change for i2505: -0.0044
2025-06-18 00:53:57 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:460 - Calculated actual price change for v2505: -0.0021
2025-06-18 00:53:57 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:460 - Calculated actual price change for pp2505: -0.0011
2025-06-18 00:53:57 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:528 - Calculated actual IV change for cu2505: -0.0426 (from 0.1748 to 0.1322)
2025-06-18 00:53:57 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:528 - Calculated actual IV change for eb2505: -0.0358 (from 0.2031 to 0.1673)
2025-06-18 00:53:57 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:528 - Calculated actual IV change for ag2505: -0.0118 (from 0.1843 to 0.1725)
2025-06-18 00:53:57 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:528 - Calculated actual IV change for sc2505: -0.0456 (from 0.2569 to 0.2114)
2025-06-18 00:53:57 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:528 - Calculated actual IV change for TA505: -0.0432 (from 0.1632 to 0.1199)
2025-06-18 00:53:57 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:528 - Calculated actual IV change for ru2505: -0.0275 (from 0.1768 to 0.1493)
2025-06-18 00:53:57 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:528 - Calculated actual IV change for RM505: 0.2389 (from 0.0001 to 0.2390)
2025-06-18 00:53:57 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:528 - Calculated actual IV change for PF505: -0.0227 (from 0.1207 to 0.0980)
2025-06-18 00:53:57 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:528 - Calculated actual IV change for m2505: 0.1596 (from 0.0001 to 0.1597)
2025-06-18 00:53:57 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:528 - Calculated actual IV change for eg2505: -0.0260 (from 0.1279 to 0.1020)
2025-06-18 00:53:57 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:528 - Calculated actual IV change for al2505: -0.0221 (from 0.1061 to 0.0840)
2025-06-18 00:53:57 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:528 - Calculated actual IV change for MA505: -0.0365 (from 0.1910 to 0.1545)
2025-06-18 00:53:57 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:528 - Calculated actual IV change for OI505: -0.0367 (from 0.1823 to 0.1456)
2025-06-18 00:53:57 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:528 - Calculated actual IV change for SR505: -0.0244 (from 0.1069 to 0.0824)
2025-06-18 00:53:57 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:528 - Calculated actual IV change for UR505: -0.0392 (from 0.2048 to 0.1656)
2025-06-18 00:53:57 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:528 - Calculated actual IV change for i2505: -0.0439 (from 0.2290 to 0.1851)
2025-06-18 00:53:57 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:528 - Calculated actual IV change for v2505: -0.0235 (from 0.1349 to 0.1114)
2025-06-18 00:53:57 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:528 - Calculated actual IV change for pp2505: -0.0025 (from 0.0818 to 0.0793)
2025-06-18 00:53:57 | INFO     | src.pnl_attribution:_calculate_total_pnl:372 - PnL归因汇总 - 主要项: -6390.12, 交叉项: -115458.10, 总计: -121848.22
2025-06-18 00:53:57 | INFO     | src.pnl_attribution:_calculate_actual_pnl:380 - Calculating actual PnL from actual option premium changes per record
2025-06-18 00:53:57 | WARNING  | src.pnl_attribution:_calculate_actual_pnl:387 - 'option_return' column not found. Calculating actual PnL from price differences.
2025-06-18 00:53:57 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-06-18 00:53:57 | INFO     | __main__:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-18 00:53:57 | INFO     | __main__:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-18 00:53:57 | INFO     | src.data_processor:prepare_database_export_data:242 - Prepared database export data: 34 records, 50 columns
2025-06-18 00:53:57 | INFO     | __main__:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-18 00:53:57 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_detail_20250618_005357.xlsx
2025-06-18 00:53:57 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_summary_20250618_005357.xlsx
2025-06-18 00:53:57 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/database_export.xlsx
2025-06-18 00:53:57 | INFO     | __main__:run_full_analysis:149 - Step 7: Exporting to database (mode: full)
2025-06-18 00:53:57 | ERROR    | src.database_exporter:connect:78 - Failed to connect to database: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost' ([Errno 61] Connection refused)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-18 00:53:57 | INFO     | src.database_exporter:close:462 - Database connection closed
2025-06-18 00:53:57 | INFO     | __main__:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-06-18 00:55:48 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-06-18 00:55:48 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-18 00:55:48 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-18 00:55:49 | INFO     | src.data_processor:load_consolidated_data:137 - Loaded 34 consolidated option records
2025-06-18 00:55:49 | INFO     | src.data_processor:merge_data:188 - Processed consolidated data: 34 records
2025-06-18 00:55:49 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-18 00:55:49 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-18 00:55:49 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-18 00:55:49 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-18 00:55:49 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:471 - Calculated actual price change for cu2505: -0.0136
2025-06-18 00:55:49 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:471 - Calculated actual price change for eb2505: 0.0041
2025-06-18 00:55:49 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:471 - Calculated actual price change for ag2505: 0.0168
2025-06-18 00:55:49 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:471 - Calculated actual price change for sc2505: 0.0030
2025-06-18 00:55:49 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:471 - Calculated actual price change for TA505: -0.0081
2025-06-18 00:55:49 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:471 - Calculated actual price change for ru2505: -0.0180
2025-06-18 00:55:49 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:471 - Calculated actual price change for RM505: -0.0127
2025-06-18 00:55:49 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:471 - Calculated actual price change for PF505: -0.0003
2025-06-18 00:55:49 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:471 - Calculated actual price change for m2505: -0.0046
2025-06-18 00:55:49 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:471 - Calculated actual price change for eg2505: -0.0087
2025-06-18 00:55:49 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:471 - Calculated actual price change for al2505: -0.0113
2025-06-18 00:55:49 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:471 - Calculated actual price change for MA505: -0.0086
2025-06-18 00:55:49 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:471 - Calculated actual price change for OI505: 0.0104
2025-06-18 00:55:49 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:471 - Calculated actual price change for SR505: 0.0020
2025-06-18 00:55:49 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:471 - Calculated actual price change for UR505: 0.0011
2025-06-18 00:55:49 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:471 - Calculated actual price change for i2505: -0.0044
2025-06-18 00:55:49 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:471 - Calculated actual price change for v2505: -0.0021
2025-06-18 00:55:49 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:471 - Calculated actual price change for pp2505: -0.0011
2025-06-18 00:55:49 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:539 - Calculated actual IV change for cu2505: -0.0426 (from 0.1748 to 0.1322)
2025-06-18 00:55:49 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:539 - Calculated actual IV change for eb2505: -0.0358 (from 0.2031 to 0.1673)
2025-06-18 00:55:49 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:539 - Calculated actual IV change for ag2505: -0.0118 (from 0.1843 to 0.1725)
2025-06-18 00:55:49 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:539 - Calculated actual IV change for sc2505: -0.0456 (from 0.2569 to 0.2114)
2025-06-18 00:55:49 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:539 - Calculated actual IV change for TA505: -0.0432 (from 0.1632 to 0.1199)
2025-06-18 00:55:49 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:539 - Calculated actual IV change for ru2505: -0.0275 (from 0.1768 to 0.1493)
2025-06-18 00:55:49 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:539 - Calculated actual IV change for RM505: 0.2389 (from 0.0001 to 0.2390)
2025-06-18 00:55:49 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:539 - Calculated actual IV change for PF505: -0.0227 (from 0.1207 to 0.0980)
2025-06-18 00:55:49 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:539 - Calculated actual IV change for m2505: 0.1596 (from 0.0001 to 0.1597)
2025-06-18 00:55:49 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:539 - Calculated actual IV change for eg2505: -0.0260 (from 0.1279 to 0.1020)
2025-06-18 00:55:49 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:539 - Calculated actual IV change for al2505: -0.0221 (from 0.1061 to 0.0840)
2025-06-18 00:55:49 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:539 - Calculated actual IV change for MA505: -0.0365 (from 0.1910 to 0.1545)
2025-06-18 00:55:49 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:539 - Calculated actual IV change for OI505: -0.0367 (from 0.1823 to 0.1456)
2025-06-18 00:55:49 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:539 - Calculated actual IV change for SR505: -0.0244 (from 0.1069 to 0.0824)
2025-06-18 00:55:49 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:539 - Calculated actual IV change for UR505: -0.0392 (from 0.2048 to 0.1656)
2025-06-18 00:55:49 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:539 - Calculated actual IV change for i2505: -0.0439 (from 0.2290 to 0.1851)
2025-06-18 00:55:49 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:539 - Calculated actual IV change for v2505: -0.0235 (from 0.1349 to 0.1114)
2025-06-18 00:55:49 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:539 - Calculated actual IV change for pp2505: -0.0025 (from 0.0818 to 0.0793)
2025-06-18 00:55:49 | INFO     | src.pnl_attribution:_calculate_total_pnl:383 - PnL归因汇总 - 主要项: -6390.12, 交叉项: -26513.16, 总计: -32903.28
2025-06-18 00:55:49 | INFO     | src.pnl_attribution:_calculate_actual_pnl:391 - Calculating actual PnL from actual option premium changes per record
2025-06-18 00:55:49 | WARNING  | src.pnl_attribution:_calculate_actual_pnl:398 - 'option_return' column not found. Calculating actual PnL from price differences.
2025-06-18 00:55:49 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-06-18 00:55:49 | INFO     | __main__:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-18 00:55:49 | INFO     | __main__:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-18 00:55:49 | INFO     | src.data_processor:prepare_database_export_data:242 - Prepared database export data: 34 records, 50 columns
2025-06-18 00:55:49 | INFO     | __main__:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-18 00:55:49 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_detail_20250618_005549.xlsx
2025-06-18 00:55:49 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_summary_20250618_005549.xlsx
2025-06-18 00:55:49 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/database_export.xlsx
2025-06-18 00:55:49 | INFO     | __main__:run_full_analysis:149 - Step 7: Exporting to database (mode: full)
2025-06-18 00:55:49 | ERROR    | src.database_exporter:connect:78 - Failed to connect to database: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost' ([Errno 61] Connection refused)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-18 00:55:49 | INFO     | src.database_exporter:close:462 - Database connection closed
2025-06-18 00:55:49 | INFO     | __main__:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-06-18 00:57:24 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-06-18 00:57:24 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-18 00:57:24 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-18 00:57:24 | INFO     | src.data_processor:load_consolidated_data:137 - Loaded 34 consolidated option records
2025-06-18 00:57:24 | INFO     | src.data_processor:merge_data:188 - Processed consolidated data: 34 records
2025-06-18 00:57:24 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-18 00:57:24 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-18 00:57:24 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-18 00:57:24 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-18 00:57:24 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:464 - Calculated actual price change for cu2505: -0.0136
2025-06-18 00:57:24 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:464 - Calculated actual price change for eb2505: 0.0041
2025-06-18 00:57:24 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:464 - Calculated actual price change for ag2505: 0.0168
2025-06-18 00:57:24 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:464 - Calculated actual price change for sc2505: 0.0030
2025-06-18 00:57:24 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:464 - Calculated actual price change for TA505: -0.0081
2025-06-18 00:57:24 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:464 - Calculated actual price change for ru2505: -0.0180
2025-06-18 00:57:24 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:464 - Calculated actual price change for RM505: -0.0127
2025-06-18 00:57:24 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:464 - Calculated actual price change for PF505: -0.0003
2025-06-18 00:57:24 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:464 - Calculated actual price change for m2505: -0.0046
2025-06-18 00:57:24 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:464 - Calculated actual price change for eg2505: -0.0087
2025-06-18 00:57:24 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:464 - Calculated actual price change for al2505: -0.0113
2025-06-18 00:57:24 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:464 - Calculated actual price change for MA505: -0.0086
2025-06-18 00:57:24 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:464 - Calculated actual price change for OI505: 0.0104
2025-06-18 00:57:24 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:464 - Calculated actual price change for SR505: 0.0020
2025-06-18 00:57:24 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:464 - Calculated actual price change for UR505: 0.0011
2025-06-18 00:57:24 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:464 - Calculated actual price change for i2505: -0.0044
2025-06-18 00:57:24 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:464 - Calculated actual price change for v2505: -0.0021
2025-06-18 00:57:24 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:464 - Calculated actual price change for pp2505: -0.0011
2025-06-18 00:57:24 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:532 - Calculated actual IV change for cu2505: -0.0426 (from 0.1748 to 0.1322)
2025-06-18 00:57:24 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:532 - Calculated actual IV change for eb2505: -0.0358 (from 0.2031 to 0.1673)
2025-06-18 00:57:24 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:532 - Calculated actual IV change for ag2505: -0.0118 (from 0.1843 to 0.1725)
2025-06-18 00:57:24 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:532 - Calculated actual IV change for sc2505: -0.0456 (from 0.2569 to 0.2114)
2025-06-18 00:57:24 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:532 - Calculated actual IV change for TA505: -0.0432 (from 0.1632 to 0.1199)
2025-06-18 00:57:24 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:532 - Calculated actual IV change for ru2505: -0.0275 (from 0.1768 to 0.1493)
2025-06-18 00:57:24 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:532 - Calculated actual IV change for RM505: 0.2389 (from 0.0001 to 0.2390)
2025-06-18 00:57:24 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:532 - Calculated actual IV change for PF505: -0.0227 (from 0.1207 to 0.0980)
2025-06-18 00:57:24 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:532 - Calculated actual IV change for m2505: 0.1596 (from 0.0001 to 0.1597)
2025-06-18 00:57:24 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:532 - Calculated actual IV change for eg2505: -0.0260 (from 0.1279 to 0.1020)
2025-06-18 00:57:24 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:532 - Calculated actual IV change for al2505: -0.0221 (from 0.1061 to 0.0840)
2025-06-18 00:57:24 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:532 - Calculated actual IV change for MA505: -0.0365 (from 0.1910 to 0.1545)
2025-06-18 00:57:24 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:532 - Calculated actual IV change for OI505: -0.0367 (from 0.1823 to 0.1456)
2025-06-18 00:57:24 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:532 - Calculated actual IV change for SR505: -0.0244 (from 0.1069 to 0.0824)
2025-06-18 00:57:24 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:532 - Calculated actual IV change for UR505: -0.0392 (from 0.2048 to 0.1656)
2025-06-18 00:57:24 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:532 - Calculated actual IV change for i2505: -0.0439 (from 0.2290 to 0.1851)
2025-06-18 00:57:24 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:532 - Calculated actual IV change for v2505: -0.0235 (from 0.1349 to 0.1114)
2025-06-18 00:57:24 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:532 - Calculated actual IV change for pp2505: -0.0025 (from 0.0818 to 0.0793)
2025-06-18 00:57:24 | INFO     | src.pnl_attribution:_calculate_total_pnl:376 - PnL归因汇总 - 主要项: -6390.12, 交叉项: 0.00, 总计: -6390.12
2025-06-18 00:57:24 | INFO     | src.pnl_attribution:_calculate_actual_pnl:384 - Calculating actual PnL from actual option premium changes per record
2025-06-18 00:57:24 | WARNING  | src.pnl_attribution:_calculate_actual_pnl:391 - 'option_return' column not found. Calculating actual PnL from price differences.
2025-06-18 00:57:24 | INFO     | src.pnl_attribution:calculate_pnl_attribution:134 - PnL attribution calculation completed
2025-06-18 00:57:24 | INFO     | __main__:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-18 00:57:24 | INFO     | __main__:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-18 00:57:24 | INFO     | src.data_processor:prepare_database_export_data:242 - Prepared database export data: 34 records, 50 columns
2025-06-18 00:57:24 | INFO     | __main__:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-18 00:57:24 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_detail_20250618_005724.xlsx
2025-06-18 00:57:24 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_summary_20250618_005724.xlsx
2025-06-18 00:57:24 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/database_export.xlsx
2025-06-18 00:57:24 | INFO     | __main__:run_full_analysis:149 - Step 7: Exporting to database (mode: full)
2025-06-18 00:57:24 | ERROR    | src.database_exporter:connect:78 - Failed to connect to database: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost' ([Errno 61] Connection refused)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-18 00:57:24 | INFO     | src.database_exporter:close:462 - Database connection closed
2025-06-18 00:57:24 | INFO     | __main__:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-06-18 00:58:53 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-06-18 00:58:53 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-18 00:58:53 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-18 00:58:53 | INFO     | src.data_processor:load_consolidated_data:137 - Loaded 34 consolidated option records
2025-06-18 00:58:53 | INFO     | src.data_processor:merge_data:188 - Processed consolidated data: 34 records
2025-06-18 00:58:53 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-18 00:58:53 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-18 00:58:53 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-18 00:58:53 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-18 00:58:53 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:351 - Calculated actual price change for cu2505: -0.0136
2025-06-18 00:58:53 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:351 - Calculated actual price change for eb2505: 0.0041
2025-06-18 00:58:53 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:351 - Calculated actual price change for ag2505: 0.0168
2025-06-18 00:58:53 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:351 - Calculated actual price change for sc2505: 0.0030
2025-06-18 00:58:53 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:351 - Calculated actual price change for TA505: -0.0081
2025-06-18 00:58:53 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:351 - Calculated actual price change for ru2505: -0.0180
2025-06-18 00:58:53 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:351 - Calculated actual price change for RM505: -0.0127
2025-06-18 00:58:53 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:351 - Calculated actual price change for PF505: -0.0003
2025-06-18 00:58:53 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:351 - Calculated actual price change for m2505: -0.0046
2025-06-18 00:58:53 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:351 - Calculated actual price change for eg2505: -0.0087
2025-06-18 00:58:53 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:351 - Calculated actual price change for al2505: -0.0113
2025-06-18 00:58:53 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:351 - Calculated actual price change for MA505: -0.0086
2025-06-18 00:58:53 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:351 - Calculated actual price change for OI505: 0.0104
2025-06-18 00:58:53 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:351 - Calculated actual price change for SR505: 0.0020
2025-06-18 00:58:53 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:351 - Calculated actual price change for UR505: 0.0011
2025-06-18 00:58:53 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:351 - Calculated actual price change for i2505: -0.0044
2025-06-18 00:58:53 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:351 - Calculated actual price change for v2505: -0.0021
2025-06-18 00:58:53 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:351 - Calculated actual price change for pp2505: -0.0011
2025-06-18 00:58:53 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:419 - Calculated actual IV change for cu2505: -0.0426 (from 0.1748 to 0.1322)
2025-06-18 00:58:53 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:419 - Calculated actual IV change for eb2505: -0.0358 (from 0.2031 to 0.1673)
2025-06-18 00:58:53 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:419 - Calculated actual IV change for ag2505: -0.0118 (from 0.1843 to 0.1725)
2025-06-18 00:58:53 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:419 - Calculated actual IV change for sc2505: -0.0456 (from 0.2569 to 0.2114)
2025-06-18 00:58:53 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:419 - Calculated actual IV change for TA505: -0.0432 (from 0.1632 to 0.1199)
2025-06-18 00:58:53 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:419 - Calculated actual IV change for ru2505: -0.0275 (from 0.1768 to 0.1493)
2025-06-18 00:58:53 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:419 - Calculated actual IV change for RM505: 0.2389 (from 0.0001 to 0.2390)
2025-06-18 00:58:53 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:419 - Calculated actual IV change for PF505: -0.0227 (from 0.1207 to 0.0980)
2025-06-18 00:58:53 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:419 - Calculated actual IV change for m2505: 0.1596 (from 0.0001 to 0.1597)
2025-06-18 00:58:53 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:419 - Calculated actual IV change for eg2505: -0.0260 (from 0.1279 to 0.1020)
2025-06-18 00:58:53 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:419 - Calculated actual IV change for al2505: -0.0221 (from 0.1061 to 0.0840)
2025-06-18 00:58:53 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:419 - Calculated actual IV change for MA505: -0.0365 (from 0.1910 to 0.1545)
2025-06-18 00:58:53 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:419 - Calculated actual IV change for OI505: -0.0367 (from 0.1823 to 0.1456)
2025-06-18 00:58:53 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:419 - Calculated actual IV change for SR505: -0.0244 (from 0.1069 to 0.0824)
2025-06-18 00:58:53 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:419 - Calculated actual IV change for UR505: -0.0392 (from 0.2048 to 0.1656)
2025-06-18 00:58:53 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:419 - Calculated actual IV change for i2505: -0.0439 (from 0.2290 to 0.1851)
2025-06-18 00:58:53 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:419 - Calculated actual IV change for v2505: -0.0235 (from 0.1349 to 0.1114)
2025-06-18 00:58:53 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:419 - Calculated actual IV change for pp2505: -0.0025 (from 0.0818 to 0.0793)
2025-06-18 00:58:53 | INFO     | src.pnl_attribution:_calculate_total_pnl:263 - PnL归因汇总 - 主要项: -6390.12, 交叉项: 0.00, 总计: -6390.12
2025-06-18 00:58:53 | INFO     | src.pnl_attribution:_calculate_actual_pnl:271 - Calculating actual PnL from actual option premium changes per record
2025-06-18 00:58:53 | WARNING  | src.pnl_attribution:_calculate_actual_pnl:278 - 'option_return' column not found. Calculating actual PnL from price differences.
2025-06-18 00:58:53 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-06-18 00:58:53 | INFO     | __main__:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-18 00:58:53 | INFO     | __main__:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-18 00:58:53 | INFO     | src.data_processor:prepare_database_export_data:242 - Prepared database export data: 34 records, 47 columns
2025-06-18 00:58:53 | INFO     | __main__:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-18 00:58:53 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_detail_20250618_005853.xlsx
2025-06-18 00:58:53 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_summary_20250618_005853.xlsx
2025-06-18 00:58:53 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/database_export.xlsx
2025-06-18 00:58:53 | INFO     | __main__:run_full_analysis:149 - Step 7: Exporting to database (mode: full)
2025-06-18 00:58:53 | ERROR    | src.database_exporter:connect:78 - Failed to connect to database: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost' ([Errno 61] Connection refused)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-18 00:58:53 | INFO     | src.database_exporter:close:462 - Database connection closed
2025-06-18 00:58:53 | INFO     | __main__:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-06-18 01:11:02 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-06-18 01:11:02 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-18 01:11:02 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-18 01:11:02 | INFO     | src.data_processor:load_consolidated_data:137 - Loaded 34 consolidated option records
2025-06-18 01:11:02 | INFO     | src.data_processor:merge_data:188 - Processed consolidated data: 34 records
2025-06-18 01:11:02 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-18 01:11:02 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-18 01:11:02 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-18 01:11:02 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-18 01:11:02 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:359 - Calculated actual price change for cu2505: -0.0136
2025-06-18 01:11:02 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:359 - Calculated actual price change for eb2505: 0.0041
2025-06-18 01:11:02 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:359 - Calculated actual price change for ag2505: 0.0168
2025-06-18 01:11:02 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:359 - Calculated actual price change for sc2505: 0.0030
2025-06-18 01:11:02 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:359 - Calculated actual price change for TA505: -0.0081
2025-06-18 01:11:02 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:359 - Calculated actual price change for ru2505: -0.0180
2025-06-18 01:11:02 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:359 - Calculated actual price change for RM505: -0.0127
2025-06-18 01:11:02 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:359 - Calculated actual price change for PF505: -0.0003
2025-06-18 01:11:02 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:359 - Calculated actual price change for m2505: -0.0046
2025-06-18 01:11:02 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:359 - Calculated actual price change for eg2505: -0.0087
2025-06-18 01:11:02 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:359 - Calculated actual price change for al2505: -0.0113
2025-06-18 01:11:02 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:359 - Calculated actual price change for MA505: -0.0086
2025-06-18 01:11:02 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:359 - Calculated actual price change for OI505: 0.0104
2025-06-18 01:11:02 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:359 - Calculated actual price change for SR505: 0.0020
2025-06-18 01:11:02 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:359 - Calculated actual price change for UR505: 0.0011
2025-06-18 01:11:02 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:359 - Calculated actual price change for i2505: -0.0044
2025-06-18 01:11:02 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:359 - Calculated actual price change for v2505: -0.0021
2025-06-18 01:11:02 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:359 - Calculated actual price change for pp2505: -0.0011
2025-06-18 01:11:02 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:427 - Calculated actual IV change for cu2505: -0.0426 (from 0.1748 to 0.1322)
2025-06-18 01:11:02 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:427 - Calculated actual IV change for eb2505: -0.0358 (from 0.2031 to 0.1673)
2025-06-18 01:11:02 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:427 - Calculated actual IV change for ag2505: -0.0118 (from 0.1843 to 0.1725)
2025-06-18 01:11:02 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:427 - Calculated actual IV change for sc2505: -0.0456 (from 0.2569 to 0.2114)
2025-06-18 01:11:02 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:427 - Calculated actual IV change for TA505: -0.0432 (from 0.1632 to 0.1199)
2025-06-18 01:11:02 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:427 - Calculated actual IV change for ru2505: -0.0275 (from 0.1768 to 0.1493)
2025-06-18 01:11:02 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:427 - Calculated actual IV change for RM505: 0.2389 (from 0.0001 to 0.2390)
2025-06-18 01:11:02 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:427 - Calculated actual IV change for PF505: -0.0227 (from 0.1207 to 0.0980)
2025-06-18 01:11:02 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:427 - Calculated actual IV change for m2505: 0.1596 (from 0.0001 to 0.1597)
2025-06-18 01:11:02 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:427 - Calculated actual IV change for eg2505: -0.0260 (from 0.1279 to 0.1020)
2025-06-18 01:11:02 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:427 - Calculated actual IV change for al2505: -0.0221 (from 0.1061 to 0.0840)
2025-06-18 01:11:02 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:427 - Calculated actual IV change for MA505: -0.0365 (from 0.1910 to 0.1545)
2025-06-18 01:11:02 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:427 - Calculated actual IV change for OI505: -0.0367 (from 0.1823 to 0.1456)
2025-06-18 01:11:02 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:427 - Calculated actual IV change for SR505: -0.0244 (from 0.1069 to 0.0824)
2025-06-18 01:11:02 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:427 - Calculated actual IV change for UR505: -0.0392 (from 0.2048 to 0.1656)
2025-06-18 01:11:02 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:427 - Calculated actual IV change for i2505: -0.0439 (from 0.2290 to 0.1851)
2025-06-18 01:11:02 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:427 - Calculated actual IV change for v2505: -0.0235 (from 0.1349 to 0.1114)
2025-06-18 01:11:02 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:427 - Calculated actual IV change for pp2505: -0.0025 (from 0.0818 to 0.0793)
2025-06-18 01:11:02 | INFO     | src.pnl_attribution:_calculate_total_pnl:271 - PnL归因汇总 - 主要项: -6390.12, 交叉项: 0.00, 总计: -6390.12
2025-06-18 01:11:02 | INFO     | src.pnl_attribution:_calculate_actual_pnl:279 - Calculating actual PnL from actual option premium changes per record
2025-06-18 01:11:02 | WARNING  | src.pnl_attribution:_calculate_actual_pnl:286 - 'option_return' column not found. Calculating actual PnL from price differences.
2025-06-18 01:11:02 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-06-18 01:11:02 | INFO     | __main__:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-18 01:11:02 | INFO     | __main__:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-18 01:11:02 | INFO     | src.data_processor:prepare_database_export_data:242 - Prepared database export data: 34 records, 47 columns
2025-06-18 01:11:02 | INFO     | __main__:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-18 01:11:02 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_detail_20250618_011102.xlsx
2025-06-18 01:11:02 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_summary_20250618_011102.xlsx
2025-06-18 01:11:02 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/database_export.xlsx
2025-06-18 01:11:02 | INFO     | __main__:run_full_analysis:149 - Step 7: Exporting to database (mode: full)
2025-06-18 01:11:02 | ERROR    | src.database_exporter:connect:78 - Failed to connect to database: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost' ([Errno 61] Connection refused)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-18 01:11:02 | INFO     | src.database_exporter:close:462 - Database connection closed
2025-06-18 01:11:02 | INFO     | __main__:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-06-18 01:14:01 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-06-18 01:14:01 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-18 01:14:01 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-18 01:14:02 | INFO     | src.data_processor:load_consolidated_data:146 - Loaded 160 consolidated option records
2025-06-18 01:14:02 | INFO     | src.data_processor:merge_data:197 - Processed consolidated data: 160 records
2025-06-18 01:14:02 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-18 01:14:02 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-18 01:14:02 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-18 01:14:02 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-18 01:14:02 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:359 - Calculated actual price change for 510050: -0.0047
2025-06-18 01:14:02 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:359 - Calculated actual price change for 510300: 0.0087
2025-06-18 01:14:02 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:359 - Calculated actual price change for 159919: -0.0127
2025-06-18 01:14:02 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:359 - Calculated actual price change for 159915: 0.0108
2025-06-18 01:14:02 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:427 - Calculated actual IV change for 510050: -2.1487 (from 2.8806 to 0.7320)
2025-06-18 01:14:02 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:427 - Calculated actual IV change for 510300: -1.9033 (from 1.9034 to 0.0001)
2025-06-18 01:14:02 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:427 - Calculated actual IV change for 159919: -2.1495 (from 3.0855 to 0.9360)
2025-06-18 01:14:02 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:427 - Calculated actual IV change for 159915: -1.6895 (from 1.6896 to 0.0001)
2025-06-18 01:14:02 | INFO     | src.pnl_attribution:_calculate_total_pnl:271 - PnL归因汇总 - 主要项: 4914297.55, 交叉项: 0.00, 总计: 4914297.55
2025-06-18 01:14:02 | INFO     | src.pnl_attribution:_calculate_actual_pnl:279 - Calculating actual PnL from actual option premium changes per record
2025-06-18 01:14:02 | WARNING  | src.pnl_attribution:_calculate_actual_pnl:286 - 'option_return' column not found. Calculating actual PnL from price differences.
2025-06-18 01:14:02 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-06-18 01:14:02 | INFO     | __main__:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-18 01:14:02 | INFO     | __main__:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-18 01:14:02 | INFO     | src.data_processor:prepare_database_export_data:251 - Prepared database export data: 160 records, 47 columns
2025-06-18 01:14:02 | INFO     | __main__:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-18 01:14:02 | INFO     | src.data_processor:save_to_excel:272 - Data saved to data/output/option_pnl_attribution_detail_20250618_011402.xlsx
2025-06-18 01:14:02 | INFO     | src.data_processor:save_to_excel:272 - Data saved to data/output/option_pnl_attribution_summary_20250618_011402.xlsx
2025-06-18 01:14:02 | INFO     | src.data_processor:save_to_excel:272 - Data saved to data/output/database_export.xlsx
2025-06-18 01:14:02 | INFO     | __main__:run_full_analysis:149 - Step 7: Exporting to database (mode: full)
2025-06-18 01:14:02 | ERROR    | src.database_exporter:connect:78 - Failed to connect to database: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost' ([Errno 61] Connection refused)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-18 01:14:02 | INFO     | src.database_exporter:close:462 - Database connection closed
2025-06-18 01:14:02 | INFO     | __main__:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-06-18 01:14:30 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-06-18 01:14:30 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-18 01:14:30 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-18 01:14:31 | INFO     | src.data_processor:load_consolidated_data:137 - Loaded 160 consolidated option records
2025-06-18 01:14:31 | INFO     | src.data_processor:merge_data:188 - Processed consolidated data: 160 records
2025-06-18 01:14:31 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-18 01:14:31 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-18 01:14:31 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-18 01:14:31 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-18 01:14:31 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:359 - Calculated actual price change for 510050: -0.0047
2025-06-18 01:14:31 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:359 - Calculated actual price change for 510300: 0.0087
2025-06-18 01:14:31 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:359 - Calculated actual price change for 159919: -0.0127
2025-06-18 01:14:31 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:359 - Calculated actual price change for 159915: 0.0108
2025-06-18 01:14:31 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:427 - Calculated actual IV change for 510050: -2.1487 (from 2.8806 to 0.7320)
2025-06-18 01:14:31 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:427 - Calculated actual IV change for 510300: -1.9033 (from 1.9034 to 0.0001)
2025-06-18 01:14:31 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:427 - Calculated actual IV change for 159919: -2.1495 (from 3.0855 to 0.9360)
2025-06-18 01:14:31 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:427 - Calculated actual IV change for 159915: -1.6895 (from 1.6896 to 0.0001)
2025-06-18 01:14:31 | INFO     | src.pnl_attribution:_calculate_total_pnl:271 - PnL归因汇总 - 主要项: 4914297.55, 交叉项: 0.00, 总计: 4914297.55
2025-06-18 01:14:31 | INFO     | src.pnl_attribution:_calculate_actual_pnl:279 - Calculating actual PnL from actual option premium changes per record
2025-06-18 01:14:31 | WARNING  | src.pnl_attribution:_calculate_actual_pnl:286 - 'option_return' column not found. Calculating actual PnL from price differences.
2025-06-18 01:14:31 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-06-18 01:14:31 | INFO     | __main__:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-18 01:14:31 | INFO     | __main__:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-18 01:14:31 | INFO     | src.data_processor:prepare_database_export_data:242 - Prepared database export data: 160 records, 47 columns
2025-06-18 01:14:31 | INFO     | __main__:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-18 01:14:31 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_detail_20250618_011431.xlsx
2025-06-18 01:14:31 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_summary_20250618_011431.xlsx
2025-06-18 01:14:31 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/database_export.xlsx
2025-06-18 01:14:31 | INFO     | __main__:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-06-18 01:14:59 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-06-18 01:14:59 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-18 01:14:59 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-18 01:14:59 | INFO     | src.data_processor:load_consolidated_data:137 - Loaded 160 consolidated option records
2025-06-18 01:14:59 | INFO     | src.data_processor:merge_data:188 - Processed consolidated data: 160 records
2025-06-18 01:14:59 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-18 01:14:59 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-18 01:14:59 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-18 01:14:59 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-18 01:14:59 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:359 - Calculated actual price change for 510050: -0.0047
2025-06-18 01:14:59 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:359 - Calculated actual price change for 510300: 0.0087
2025-06-18 01:14:59 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:359 - Calculated actual price change for 159919: -0.0127
2025-06-18 01:14:59 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:359 - Calculated actual price change for 159915: 0.0108
2025-06-18 01:14:59 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:427 - Calculated actual IV change for 510050: -2.1487 (from 2.8806 to 0.7320)
2025-06-18 01:14:59 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:427 - Calculated actual IV change for 510300: -1.9033 (from 1.9034 to 0.0001)
2025-06-18 01:14:59 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:427 - Calculated actual IV change for 159919: -2.1495 (from 3.0855 to 0.9360)
2025-06-18 01:14:59 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:427 - Calculated actual IV change for 159915: -1.6895 (from 1.6896 to 0.0001)
2025-06-18 01:14:59 | INFO     | src.pnl_attribution:_calculate_total_pnl:271 - PnL归因汇总 - 主要项: 4914297.55, 交叉项: 0.00, 总计: 4914297.55
2025-06-18 01:14:59 | INFO     | src.pnl_attribution:_calculate_actual_pnl:279 - Calculating actual PnL from actual option premium changes per record
2025-06-18 01:14:59 | WARNING  | src.pnl_attribution:_calculate_actual_pnl:286 - 'option_return' column not found. Calculating actual PnL from price differences.
2025-06-18 01:14:59 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-06-18 01:14:59 | INFO     | __main__:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-18 01:14:59 | INFO     | __main__:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-18 01:14:59 | INFO     | src.data_processor:prepare_database_export_data:242 - Prepared database export data: 160 records, 47 columns
2025-06-18 01:14:59 | INFO     | __main__:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-18 01:14:59 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_detail_20250618_011459.xlsx
2025-06-18 01:14:59 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_summary_20250618_011459.xlsx
2025-06-18 01:15:00 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/database_export.xlsx
2025-06-18 01:15:00 | INFO     | __main__:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-06-18 01:15:43 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-06-18 01:15:43 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-18 01:15:43 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-18 01:15:43 | INFO     | src.data_processor:load_consolidated_data:137 - Loaded 160 consolidated option records
2025-06-18 01:15:43 | INFO     | src.data_processor:merge_data:188 - Processed consolidated data: 160 records
2025-06-18 01:15:43 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-18 01:15:43 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-18 01:15:43 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-18 01:15:43 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-18 01:15:43 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:359 - Calculated actual price change for 510050: -0.0047
2025-06-18 01:15:43 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:359 - Calculated actual price change for 510300: 0.0087
2025-06-18 01:15:43 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:359 - Calculated actual price change for 159919: -0.0127
2025-06-18 01:15:43 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:359 - Calculated actual price change for 159915: 0.0108
2025-06-18 01:15:43 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:427 - Calculated actual IV change for 510050: -2.1487 (from 2.8806 to 0.7320)
2025-06-18 01:15:43 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:427 - Calculated actual IV change for 510300: -1.9033 (from 1.9034 to 0.0001)
2025-06-18 01:15:43 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:427 - Calculated actual IV change for 159919: -2.1495 (from 3.0855 to 0.9360)
2025-06-18 01:15:43 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:427 - Calculated actual IV change for 159915: -1.6895 (from 1.6896 to 0.0001)
2025-06-18 01:15:43 | INFO     | src.pnl_attribution:_calculate_total_pnl:271 - PnL归因汇总 - 主要项: 4914297.55, 交叉项: 0.00, 总计: 4914297.55
2025-06-18 01:15:43 | INFO     | src.pnl_attribution:_calculate_actual_pnl:279 - Calculating actual PnL from actual option premium changes per record
2025-06-18 01:15:43 | WARNING  | src.pnl_attribution:_calculate_actual_pnl:286 - 'option_return' column not found. Calculating actual PnL from price differences.
2025-06-18 01:15:43 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-06-18 01:15:43 | INFO     | __main__:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-18 01:15:43 | INFO     | __main__:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-18 01:15:43 | INFO     | src.data_processor:prepare_database_export_data:242 - Prepared database export data: 160 records, 47 columns
2025-06-18 01:15:43 | INFO     | __main__:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-18 01:15:43 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_detail_20250618_011543.xlsx
2025-06-18 01:15:43 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_summary_20250618_011543.xlsx
2025-06-18 01:15:43 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/database_export.xlsx
2025-06-18 01:15:43 | INFO     | __main__:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-06-18 01:18:12 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-06-18 01:18:12 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-18 01:18:12 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-18 01:18:12 | INFO     | src.data_processor:load_consolidated_data:137 - Loaded 160 consolidated option records
2025-06-18 01:18:12 | INFO     | src.data_processor:merge_data:188 - Processed consolidated data: 160 records
2025-06-18 01:18:12 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-18 01:18:12 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-18 01:18:12 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-18 01:18:12 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-18 01:18:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:359 - Calculated actual price change for 510050: -0.0047
2025-06-18 01:18:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:359 - Calculated actual price change for 510300: 0.0087
2025-06-18 01:18:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:359 - Calculated actual price change for 159919: -0.0127
2025-06-18 01:18:12 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:359 - Calculated actual price change for 159915: 0.0108
2025-06-18 01:18:12 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:427 - Calculated actual IV change for 510050: -2.1487 (from 2.8806 to 0.7320)
2025-06-18 01:18:12 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:427 - Calculated actual IV change for 510300: -1.9033 (from 1.9034 to 0.0001)
2025-06-18 01:18:12 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:427 - Calculated actual IV change for 159919: -2.1495 (from 3.0855 to 0.9360)
2025-06-18 01:18:12 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:427 - Calculated actual IV change for 159915: -1.6895 (from 1.6896 to 0.0001)
2025-06-18 01:18:12 | INFO     | src.pnl_attribution:_calculate_total_pnl:271 - PnL归因汇总 - 主要项: 4914297.55, 交叉项: 0.00, 总计: 4914297.55
2025-06-18 01:18:12 | INFO     | src.pnl_attribution:_calculate_actual_pnl:279 - Calculating actual PnL from actual option premium changes per record
2025-06-18 01:18:12 | WARNING  | src.pnl_attribution:_calculate_actual_pnl:286 - 'option_return' column not found. Calculating actual PnL from price differences.
2025-06-18 01:18:12 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-06-18 01:18:12 | INFO     | __main__:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-18 01:18:12 | INFO     | __main__:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-18 01:18:12 | INFO     | src.data_processor:prepare_database_export_data:242 - Prepared database export data: 160 records, 47 columns
2025-06-18 01:18:12 | INFO     | __main__:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-18 01:18:12 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_detail_20250618_011812.xlsx
2025-06-18 01:18:12 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_summary_20250618_011812.xlsx
2025-06-18 01:18:12 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/database_export.xlsx
2025-06-18 01:18:12 | INFO     | __main__:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-06-18 01:18:43 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-06-18 01:18:43 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-18 01:18:43 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-18 01:18:43 | INFO     | src.data_processor:load_consolidated_data:137 - Loaded 160 consolidated option records
2025-06-18 01:18:43 | INFO     | src.data_processor:merge_data:188 - Processed consolidated data: 160 records
2025-06-18 01:18:43 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-18 01:18:43 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-18 01:18:43 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-18 01:18:43 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-18 01:18:43 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:359 - Calculated actual price change for 510050: -0.0047
2025-06-18 01:18:43 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:359 - Calculated actual price change for 510300: 0.0087
2025-06-18 01:18:43 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:359 - Calculated actual price change for 159919: -0.0127
2025-06-18 01:18:43 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:359 - Calculated actual price change for 159915: 0.0108
2025-06-18 01:18:43 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:427 - Calculated actual IV change for 510050: -2.1487 (from 2.8806 to 0.7320)
2025-06-18 01:18:43 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:427 - Calculated actual IV change for 510300: -1.9033 (from 1.9034 to 0.0001)
2025-06-18 01:18:43 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:427 - Calculated actual IV change for 159919: -2.1495 (from 3.0855 to 0.9360)
2025-06-18 01:18:43 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:427 - Calculated actual IV change for 159915: -1.6895 (from 1.6896 to 0.0001)
2025-06-18 01:18:43 | INFO     | src.pnl_attribution:_calculate_total_pnl:271 - PnL归因汇总 - 主要项: 4914297.55, 交叉项: 0.00, 总计: 4914297.55
2025-06-18 01:18:43 | INFO     | src.pnl_attribution:_calculate_actual_pnl:279 - Calculating actual PnL from actual option premium changes per record
2025-06-18 01:18:43 | WARNING  | src.pnl_attribution:_calculate_actual_pnl:286 - 'option_return' column not found. Calculating actual PnL from price differences.
2025-06-18 01:18:43 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-06-18 01:18:43 | INFO     | __main__:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-18 01:18:43 | INFO     | __main__:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-18 01:18:43 | INFO     | src.data_processor:prepare_database_export_data:242 - Prepared database export data: 160 records, 47 columns
2025-06-18 01:18:43 | INFO     | __main__:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-18 01:18:43 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_detail_20250618_011843.xlsx
2025-06-18 01:18:43 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_summary_20250618_011843.xlsx
2025-06-18 01:18:43 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/database_export.xlsx
2025-06-18 01:18:43 | INFO     | __main__:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-06-18 01:26:51 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-06-18 01:26:51 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-18 01:26:51 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-18 01:26:51 | INFO     | src.data_processor:load_consolidated_data:137 - Loaded 160 consolidated option records
2025-06-18 01:26:51 | INFO     | src.data_processor:merge_data:188 - Processed consolidated data: 160 records
2025-06-18 01:26:51 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-18 01:26:51 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-18 01:26:51 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-18 01:26:51 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-18 01:26:51 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:359 - Calculated actual price change for 510050: -0.0047
2025-06-18 01:26:51 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:359 - Calculated actual price change for 510300: 0.0087
2025-06-18 01:26:51 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:359 - Calculated actual price change for 159919: -0.0127
2025-06-18 01:26:51 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:359 - Calculated actual price change for 159915: 0.0108
2025-06-18 01:26:51 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:427 - Calculated actual IV change for 510050: -2.1487 (from 2.8806 to 0.7320)
2025-06-18 01:26:51 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:427 - Calculated actual IV change for 510300: -1.9033 (from 1.9034 to 0.0001)
2025-06-18 01:26:51 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:427 - Calculated actual IV change for 159919: -2.1495 (from 3.0855 to 0.9360)
2025-06-18 01:26:51 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:427 - Calculated actual IV change for 159915: -1.6895 (from 1.6896 to 0.0001)
2025-06-18 01:26:51 | INFO     | src.pnl_attribution:_calculate_total_pnl:271 - PnL归因汇总 - 主要项: 4914297.55, 交叉项: 0.00, 总计: 4914297.55
2025-06-18 01:26:51 | INFO     | src.pnl_attribution:_calculate_actual_pnl:279 - Calculating actual PnL from actual option premium changes per record
2025-06-18 01:26:51 | WARNING  | src.pnl_attribution:_calculate_actual_pnl:286 - 'option_return' column not found. Calculating actual PnL from price differences.
2025-06-18 01:26:51 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-06-18 01:26:51 | INFO     | __main__:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-18 01:26:51 | INFO     | __main__:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-18 01:26:51 | INFO     | src.data_processor:prepare_database_export_data:242 - Prepared database export data: 160 records, 47 columns
2025-06-18 01:26:51 | INFO     | __main__:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-18 01:26:51 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_detail_20250618_012651.xlsx
2025-06-18 01:26:51 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_summary_20250618_012651.xlsx
2025-06-18 01:26:51 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/database_export.xlsx
2025-06-18 01:26:51 | INFO     | __main__:run_full_analysis:149 - Step 7: Exporting to database (mode: full)
2025-06-18 01:26:51 | ERROR    | src.database_exporter:connect:78 - Failed to connect to database: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost' ([Errno 61] Connection refused)")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-18 01:26:51 | INFO     | src.database_exporter:close:462 - Database connection closed
2025-06-18 01:26:51 | INFO     | __main__:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-06-18 01:38:22 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-06-18 01:38:22 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-18 01:38:22 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-18 01:38:22 | INFO     | src.data_processor:load_consolidated_data:137 - Loaded 160 consolidated option records
2025-06-18 01:38:22 | INFO     | src.data_processor:merge_data:188 - Processed consolidated data: 160 records
2025-06-18 01:38:22 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-18 01:38:22 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-18 01:38:22 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-18 01:38:22 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-18 01:38:22 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:359 - Calculated actual price change for 510050: -0.0047
2025-06-18 01:38:22 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:359 - Calculated actual price change for 510300: 0.0087
2025-06-18 01:38:22 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:359 - Calculated actual price change for 159919: -0.0127
2025-06-18 01:38:22 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:359 - Calculated actual price change for 159915: 0.0108
2025-06-18 01:38:22 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:427 - Calculated actual IV change for 510050: -2.1487 (from 2.8806 to 0.7320)
2025-06-18 01:38:22 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:427 - Calculated actual IV change for 510300: -1.9033 (from 1.9034 to 0.0001)
2025-06-18 01:38:22 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:427 - Calculated actual IV change for 159919: -2.1495 (from 3.0855 to 0.9360)
2025-06-18 01:38:22 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:427 - Calculated actual IV change for 159915: -1.6895 (from 1.6896 to 0.0001)
2025-06-18 01:38:22 | INFO     | src.pnl_attribution:_calculate_total_pnl:271 - PnL归因汇总 - 主要项: 4914297.55, 交叉项: 0.00, 总计: 4914297.55
2025-06-18 01:38:22 | INFO     | src.pnl_attribution:_calculate_actual_pnl:279 - Calculating actual PnL from actual option premium changes per record
2025-06-18 01:38:22 | WARNING  | src.pnl_attribution:_calculate_actual_pnl:286 - 'option_return' column not found. Calculating actual PnL from price differences.
2025-06-18 01:38:22 | INFO     | src.pnl_attribution:calculate_pnl_attribution:130 - PnL attribution calculation completed
2025-06-18 01:38:22 | INFO     | __main__:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-18 01:38:22 | INFO     | __main__:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-18 01:38:22 | INFO     | src.data_processor:prepare_database_export_data:242 - Prepared database export data: 160 records, 47 columns
2025-06-18 01:38:22 | INFO     | __main__:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-18 01:38:23 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_detail_20250618_013822.xlsx
2025-06-18 01:38:23 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/option_pnl_attribution_summary_20250618_013822.xlsx
2025-06-18 01:38:23 | INFO     | src.data_processor:save_to_excel:263 - Data saved to data/output/database_export.xlsx
2025-06-18 01:38:23 | INFO     | __main__:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-06-18 01:42:03 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-06-18 01:42:03 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-18 01:42:03 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-18 01:42:03 | INFO     | src.data_processor:load_consolidated_data:137 - Loaded 34 consolidated option records
2025-06-18 01:42:03 | INFO     | src.data_processor:merge_data:191 - Processed consolidated data: 34 records
2025-06-18 01:42:03 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-18 01:42:03 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-18 01:42:03 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:435 - Calculated actual price change for cu2505: -0.0136
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:435 - Calculated actual price change for eb2505: 0.0041
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:435 - Calculated actual price change for ag2505: 0.0168
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:435 - Calculated actual price change for sc2505: 0.0030
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:435 - Calculated actual price change for TA505: -0.0081
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:435 - Calculated actual price change for ru2505: -0.0180
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:435 - Calculated actual price change for RM505: -0.0127
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:435 - Calculated actual price change for PF505: -0.0003
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:435 - Calculated actual price change for m2505: -0.0046
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:435 - Calculated actual price change for eg2505: -0.0087
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:435 - Calculated actual price change for al2505: -0.0113
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:435 - Calculated actual price change for MA505: -0.0086
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:435 - Calculated actual price change for OI505: 0.0104
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:435 - Calculated actual price change for SR505: 0.0020
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:435 - Calculated actual price change for UR505: 0.0011
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:435 - Calculated actual price change for i2505: -0.0044
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:435 - Calculated actual price change for v2505: -0.0021
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_calculate_actual_price_changes:435 - Calculated actual price change for pp2505: -0.0011
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:509 - Calculated actual IV change for cu2505: -0.0426 (from 0.1748 to 0.1322)
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:509 - Calculated actual IV change for eb2505: -0.0358 (from 0.2031 to 0.1673)
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:509 - Calculated actual IV change for ag2505: -0.0118 (from 0.1843 to 0.1725)
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:509 - Calculated actual IV change for sc2505: -0.0456 (from 0.2569 to 0.2114)
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:509 - Calculated actual IV change for TA505: -0.0432 (from 0.1632 to 0.1199)
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:509 - Calculated actual IV change for ru2505: -0.0275 (from 0.1768 to 0.1493)
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:517 - Using estimated IV change for RM505: -0.0013 (based on price change -0.0127)
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:509 - Calculated actual IV change for PF505: -0.0227 (from 0.1207 to 0.0980)
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:517 - Using estimated IV change for m2505: -0.0005 (based on price change -0.0046)
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:509 - Calculated actual IV change for eg2505: -0.0260 (from 0.1279 to 0.1020)
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:509 - Calculated actual IV change for al2505: -0.0221 (from 0.1061 to 0.0840)
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:509 - Calculated actual IV change for MA505: -0.0365 (from 0.1910 to 0.1545)
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:509 - Calculated actual IV change for OI505: -0.0367 (from 0.1823 to 0.1456)
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:509 - Calculated actual IV change for SR505: -0.0244 (from 0.1069 to 0.0824)
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:509 - Calculated actual IV change for UR505: -0.0392 (from 0.2048 to 0.1656)
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:509 - Calculated actual IV change for i2505: -0.0439 (from 0.2290 to 0.1851)
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:509 - Calculated actual IV change for v2505: -0.0235 (from 0.1349 to 0.1114)
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_calculate_actual_vol_changes:509 - Calculated actual IV change for pp2505: -0.0025 (from 0.0818 to 0.0793)
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_calculate_total_pnl:338 - PnL归因汇总 - 主要项: 112118.67, 交叉项: 0.00, 总计: 112118.67
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_calculate_actual_pnl:355 - Calculating actual PnL using mathematical framework: (Pt - P0) × Q × M
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_calculate_actual_pnl:361 - Actual PnL calculation summary:
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_calculate_actual_pnl:362 -   - Total actual PnL: 51832.50
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_calculate_actual_pnl:363 -   - Price differences range: [-54.0000, 110.0000]
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_calculate_actual_pnl:364 -   - Position range: [-350, -1]
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_log_pnl_attribution_summary:138 - === PnL Attribution Summary ===
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_log_pnl_attribution_summary:147 - Delta PnL: 25,521.54
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_log_pnl_attribution_summary:147 - Gamma PnL: -13,831.22
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_log_pnl_attribution_summary:147 - Vega PnL: 91,245.26
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_log_pnl_attribution_summary:147 - Theta PnL: 9,183.10
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_log_pnl_attribution_summary:147 - Rho PnL: 0.00
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_log_pnl_attribution_summary:153 - Total Attributed PnL: 112,118.67
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_log_pnl_attribution_summary:154 - Total Calculated PnL: 112,118.67
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_log_pnl_attribution_summary:155 - Actual PnL: 51,832.50
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_log_pnl_attribution_summary:156 - Unexplained PnL: -60,286.17
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_log_pnl_attribution_summary:160 - Explanation Ratio: 2.1631 (216.31%)
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:_log_pnl_attribution_summary:164 - === End PnL Attribution Summary ===
2025-06-18 01:42:03 | INFO     | src.pnl_attribution:calculate_pnl_attribution:133 - PnL attribution calculation completed
2025-06-18 01:42:03 | INFO     | __main__:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-18 01:42:03 | INFO     | __main__:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-18 01:42:03 | INFO     | src.data_processor:prepare_database_export_data:245 - Prepared database export data: 34 records, 48 columns
2025-06-18 01:42:03 | INFO     | __main__:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-18 01:42:03 | INFO     | src.data_processor:save_to_excel:266 - Data saved to data/output/option_pnl_attribution_detail_20250618_014203.xlsx
2025-06-18 01:42:03 | INFO     | src.data_processor:save_to_excel:266 - Data saved to data/output/option_pnl_attribution_summary_20250618_014203.xlsx
2025-06-18 01:42:03 | INFO     | src.data_processor:save_to_excel:266 - Data saved to data/output/database_export.xlsx
2025-06-18 01:42:03 | INFO     | __main__:run_full_analysis:166 - Full PnL attribution analysis completed successfully
2025-06-18 01:42:23 | INFO     | __main__:__init__:49 - Option PnL Attribution System initialized
2025-06-18 01:42:23 | INFO     | src.data_processor:load_consolidated_data:137 - Loaded 34 consolidated option records
2025-06-18 01:42:23 | INFO     | src.data_processor:load_consolidated_data:137 - Loaded 34 consolidated option records
2025-06-18 01:42:23 | INFO     | __main__:run_full_analysis:106 - Starting full PnL attribution analysis
2025-06-18 01:42:23 | INFO     | __main__:run_full_analysis:110 - Step 1: Loading and processing consolidated data
2025-06-18 01:42:23 | INFO     | src.data_processor:load_consolidated_data:137 - Loaded 34 consolidated option records
2025-06-18 01:42:23 | INFO     | src.data_processor:merge_data:191 - Processed consolidated data: 34 records
2025-06-18 01:42:23 | INFO     | __main__:run_full_analysis:123 - Step 2: Calculating implied volatility and Greeks
2025-06-18 01:42:23 | INFO     | __main__:_calculate_greeks_with_iv:176 - Calculating implied volatility
2025-06-18 01:42:23 | INFO     | __main__:run_full_analysis:127 - Step 3: Calculating PnL attribution
2025-06-18 01:42:23 | INFO     | src.pnl_attribution:calculate_pnl_attribution:80 - Starting PnL attribution calculation
2025-06-18 01:42:23 | INFO     | src.pnl_attribution:_calculate_total_pnl:338 - PnL归因汇总 - 主要项: 14087.82, 交叉项: 0.00, 总计: 14087.82
2025-06-18 01:42:23 | INFO     | src.pnl_attribution:_calculate_actual_pnl:355 - Calculating actual PnL using mathematical framework: (Pt - P0) × Q × M
2025-06-18 01:42:23 | INFO     | src.pnl_attribution:_calculate_actual_pnl:361 - Actual PnL calculation summary:
2025-06-18 01:42:23 | INFO     | src.pnl_attribution:_calculate_actual_pnl:362 -   - Total actual PnL: 51832.50
2025-06-18 01:42:23 | INFO     | src.pnl_attribution:_calculate_actual_pnl:363 -   - Price differences range: [-54.0000, 110.0000]
2025-06-18 01:42:23 | INFO     | src.pnl_attribution:_calculate_actual_pnl:364 -   - Position range: [-350, -1]
2025-06-18 01:42:23 | INFO     | src.pnl_attribution:_log_pnl_attribution_summary:138 - === PnL Attribution Summary ===
2025-06-18 01:42:23 | INFO     | src.pnl_attribution:_log_pnl_attribution_summary:147 - Delta PnL: 91,439.00
2025-06-18 01:42:23 | INFO     | src.pnl_attribution:_log_pnl_attribution_summary:147 - Gamma PnL: -53,294.32
2025-06-18 01:42:23 | INFO     | src.pnl_attribution:_log_pnl_attribution_summary:147 - Vega PnL: -33,239.97
2025-06-18 01:42:23 | INFO     | src.pnl_attribution:_log_pnl_attribution_summary:147 - Theta PnL: 9,183.10
2025-06-18 01:42:23 | INFO     | src.pnl_attribution:_log_pnl_attribution_summary:147 - Rho PnL: 0.00
2025-06-18 01:42:23 | INFO     | src.pnl_attribution:_log_pnl_attribution_summary:153 - Total Attributed PnL: 14,087.82
2025-06-18 01:42:23 | INFO     | src.pnl_attribution:_log_pnl_attribution_summary:154 - Total Calculated PnL: 14,087.82
2025-06-18 01:42:23 | INFO     | src.pnl_attribution:_log_pnl_attribution_summary:155 - Actual PnL: 51,832.50
2025-06-18 01:42:23 | INFO     | src.pnl_attribution:_log_pnl_attribution_summary:156 - Unexplained PnL: 37,744.68
2025-06-18 01:42:23 | INFO     | src.pnl_attribution:_log_pnl_attribution_summary:160 - Explanation Ratio: 0.2718 (27.18%)
2025-06-18 01:42:23 | INFO     | src.pnl_attribution:_log_pnl_attribution_summary:164 - === End PnL Attribution Summary ===
2025-06-18 01:42:23 | INFO     | src.pnl_attribution:calculate_pnl_attribution:133 - PnL attribution calculation completed
2025-06-18 01:42:23 | INFO     | __main__:run_full_analysis:133 - Step 4: Generating summary reports
2025-06-18 01:42:23 | INFO     | __main__:run_full_analysis:137 - Step 5: Preparing database export data
2025-06-18 01:42:23 | INFO     | src.data_processor:prepare_database_export_data:245 - Prepared database export data: 34 records, 48 columns
2025-06-18 01:42:23 | INFO     | __main__:run_full_analysis:143 - Step 6: Saving results to Excel
2025-06-18 01:42:23 | INFO     | src.data_processor:save_to_excel:266 - Data saved to data/output/option_pnl_attribution_detail_20250618_014223.xlsx
2025-06-18 01:42:23 | INFO     | src.data_processor:save_to_excel:266 - Data saved to data/output/option_pnl_attribution_summary_20250618_014223.xlsx
2025-06-18 01:42:23 | INFO     | src.data_processor:save_to_excel:266 - Data saved to data/output/database_export.xlsx
2025-06-18 01:42:23 | INFO     | __main__:run_full_analysis:166 - Full PnL attribution analysis completed successfully
