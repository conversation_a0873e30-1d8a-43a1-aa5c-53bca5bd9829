"""
数据处理模块
负责加载、清洗和合并期权数据
"""

import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
import yaml
from loguru import logger


class DataProcessor:
    """数据处理器"""

    def __init__(self, config_path: str = "config/config.yaml"):
        """
        初始化数据处理器

        Args:
            config_path: 配置文件路径
        """
        self.config = self._load_config(config_path)
        self.input_dir = Path(self.config['data_files']['input_directory'])
        self.output_dir = Path(self.config['data_files']['output_directory'])

        # 创建输入和输出目录
        self.input_dir.mkdir(parents=True, exist_ok=True)
        self.output_dir.mkdir(parents=True, exist_ok=True)

        logger.info("Data processor initialized")

    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"Failed to load config: {e}")
            return self._get_default_config()

    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            'data_files': {
                'input_directory': 'data/input',
                'output_directory': 'data/output',
                'position_file': 'positions.xlsx',
                'market_data_file': 'market_data.xlsx'
            },
            'calculation': {
                'risk_free_rate': 0.015,
                'trading_days_per_year': 252
            }
        }

    def create_sample_data(self) -> None:
        """创建示例数据文件"""
        logger.info("Creating sample data files")

        # 创建输入目录
        self.input_dir.mkdir(parents=True, exist_ok=True)

        # 创建示例合并数据
        self._create_sample_consolidated_data()

        logger.info("Sample data files created successfully")

    def _create_sample_consolidated_data(self) -> None:
        """创建示例合并数据"""
        # 生成示例期权合约
        contracts = []

        # 不同的标的资产
        underlyings = ['510050', '510300', '159919', '159915']

        contract_id = 1

        for i, underlying in enumerate(underlyings):
            # 当前价格
            current_underlying_price = 3.0 + i * 0.5  # 3.0, 3.5, 4.0, 4.5
            previous_underlying_price = current_underlying_price * np.random.uniform(0.98, 1.02)

            # 生成不同行权价的期权
            strikes = [current_underlying_price * k for k in [0.9, 0.95, 1.0, 1.05, 1.1]]

            for strike in strikes:
                for option_type in ['call', 'put']:
                    # 生成不同到期日的合约
                    for days_to_expiry in [7, 14, 30, 60]:
                        expiry_date = datetime.now() + timedelta(days=days_to_expiry)

                        # 随机生成持仓
                        position = np.random.choice([-50, -20, -10, 10, 20, 50])

                        # 生成期权价格数据
                        option_current_price = round(max(0.001, np.random.uniform(0.01, 0.5)), 4)
                        option_previous_price = option_current_price * np.random.uniform(0.95, 1.05)

                        contracts.append({
                            'contract_id': f'OPT_{contract_id:06d}',
                            'underlying_code': underlying,
                            'option_type': option_type,
                            'strike_price': round(strike, 3),
                            'expiry_date': expiry_date.strftime('%Y-%m-%d'),
                            'position': position,
                            'contract_multiplier': 10000,
                            'option_previous_close_price': round(option_previous_price, 4),
                            'option_current_close_price': option_current_price,
                            'underlying_previous_close_price': round(previous_underlying_price, 4),
                            'underlying_current_close_price': round(current_underlying_price, 4),
                            'trade_date': datetime.now().strftime('%Y-%m-%d'),
                            'portfolio': f'Portfolio_{(i % 3) + 1}'
                        })

                        contract_id += 1

        # 保存到Excel文件
        df_consolidated = pd.DataFrame(contracts)
        consolidated_file = self.input_dir / "consolidated_option_data.xlsx"
        df_consolidated.to_excel(consolidated_file, index=False)

        logger.info(f"Created {len(contracts)} sample option contracts in {consolidated_file}")

    def load_consolidated_data(self) -> pd.DataFrame:
        """加载合并数据"""
        consolidated_file = self.input_dir / "consolidated_option_data.xlsx"

        if not consolidated_file.exists():
            logger.warning(f"Consolidated data file not found: {consolidated_file}")
            self.create_sample_data()

        try:
            df = pd.read_excel(consolidated_file)
            logger.info(f"Loaded {len(df)} consolidated option records")
            return df
        except Exception as e:
            logger.error(f"Failed to load consolidated data: {e}")
            return pd.DataFrame()

    def load_positions_data(self) -> pd.DataFrame:
        """加载持仓数据 - 现在使用合并数据"""
        logger.warning("load_positions_data is deprecated. Use load_consolidated_data instead.")
        return self.load_consolidated_data()

    def load_market_data(self) -> pd.DataFrame:
        """加载市场数据 - 现在使用合并数据"""
        logger.warning("load_market_data is deprecated. Use load_consolidated_data instead.")
        return pd.DataFrame()  # 返回空DataFrame，因为数据已合并

    def merge_data(self, positions_df: pd.DataFrame, market_df: pd.DataFrame,
                   target_date: Optional[str] = None) -> pd.DataFrame:
        """
        处理合并数据（现在数据已经是合并的）

        Args:
            positions_df: 合并数据（忽略market_df）
            market_df: 市场数据（已废弃，忽略）
            target_date: 目标日期，如果为None则使用最新日期

        Returns:
            处理后的数据框
        """
        # 现在数据已经是合并的，直接处理
        merged_df = positions_df.copy()

        # 计算到期时间（年化）
        merged_df['expiry_date'] = pd.to_datetime(merged_df['expiry_date'])
        merged_df['trade_date'] = pd.to_datetime(merged_df['trade_date'])
        merged_df['days_to_expiry'] = (merged_df['expiry_date'] - merged_df['trade_date']).dt.days
        merged_df['time_to_expiry'] = merged_df['days_to_expiry'] / self.config['calculation']['trading_days_per_year']

        # 转换期权类型为数值
        merged_df['option_type_num'] = merged_df['option_type'].map({'call': 1, 'put': -1})

        # 添加计算所需的字段
        merged_df['underlying_price'] = merged_df['underlying_current_close_price']
        merged_df['risk_free_rate'] = self.config['calculation']['risk_free_rate']

        # 计算期权收益（按照数学框架：(Pt - P0) × Q × M）
        merged_df['option_return'] = (
            (merged_df['option_current_close_price'] - merged_df['option_previous_close_price']) *
            merged_df['position'] * merged_df['contract_multiplier']
        )

        # 保持向后兼容性
        merged_df['total_option_return'] = merged_df['option_return']

        logger.info(f"Processed consolidated data: {len(merged_df)} records")
        return merged_df

    def prepare_database_export_data(self, merged_df: pd.DataFrame,
                                   greeks_data: Dict, pnl_attribution: Dict) -> pd.DataFrame:
        """
        准备用于数据库导出的完整数据

        Args:
            merged_df: 合并后的基础数据
            greeks_data: 希腊值数据
            pnl_attribution: PnL归因数据

        Returns:
            完整的数据库导出数据框
        """
        # 复制基础数据
        export_df = merged_df.copy()

        # 添加希腊值
        for greek_name, values in greeks_data.items():
            export_df[greek_name] = values

        # 添加PnL归因数据
        for attr_name, values in pnl_attribution.items():
            export_df[f'pnl_{attr_name}'] = values

        # 添加计算时间戳
        export_df['calculation_timestamp'] = datetime.now()

        # 重新排列列顺序，将重要字段放在前面
        important_columns = [
            'contract_id', 'underlying_code', 'underlying_name', 'option_type',
            'strike_price', 'expiry_date', 'days_to_expiry', 'time_to_expiry',
            'position', 'contract_multiplier', 'underlying_price',
            'implied_volatility', 'risk_free_rate'
        ]

        # 希腊值列
        greek_columns = [col for col in export_df.columns if col in greeks_data.keys()]

        # PnL归因列
        pnl_columns = [col for col in export_df.columns if col.startswith('pnl_')]

        # 其他列
        other_columns = [col for col in export_df.columns
                        if col not in important_columns + greek_columns + pnl_columns]

        # 重新排列列
        final_columns = important_columns + greek_columns + pnl_columns + other_columns
        final_columns = [col for col in final_columns if col in export_df.columns]

        export_df = export_df[final_columns]

        logger.info(f"Prepared database export data: {len(export_df)} records, {len(final_columns)} columns")
        return export_df

    def save_to_excel(self, df: pd.DataFrame, filename: str, sheet_name: str = 'Data') -> str:
        """
        保存数据到Excel文件

        Args:
            df: 要保存的数据框
            filename: 文件名
            sheet_name: 工作表名称

        Returns:
            保存的文件路径
        """
        file_path = self.output_dir / filename

        try:
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name=sheet_name, index=False)

            logger.info(f"Data saved to {file_path}")
            return str(file_path)
        except Exception as e:
            logger.error(f"Failed to save data to Excel: {e}")
            return ""

    def get_required_fields(self) -> Dict[str, List[str]]:
        """
        获取所需的数据字段列表

        Returns:
            包含各类数据所需字段的字典
        """
        return {
            'consolidated_data': [
                'contract_id',                    # 合约ID
                'underlying_code',               # 标的代码
                'option_type',                   # 期权类型 (call/put)
                'strike_price',                  # 行权价
                'expiry_date',                   # 到期日
                'position',                      # 持仓数量
                'contract_multiplier',           # 合约乘数
                'option_previous_close_price',   # 期权前一交易日收盘价
                'option_current_close_price',    # 期权当前交易日收盘价
                'underlying_previous_close_price', # 标的资产前一交易日收盘价
                'underlying_current_close_price',  # 标的资产当前交易日收盘价
                'trade_date',                    # 交易日期
                'portfolio'                      # 投资组合
            ],
            'calculated_fields': [
                'days_to_expiry',                # 到期天数
                'time_to_expiry',                # 到期时间(年化)
                'option_type_num',               # 期权类型数值
                'underlying_price',              # 标的价格
                'risk_free_rate',                # 无风险利率
                'total_option_return',           # 期权总收益
                'implied_volatility',            # 计算得出的隐含波动率
                'price',                         # 理论价格
                'delta',                         # Delta
                'gamma',                         # Gamma
                'vega',                          # Vega
                'theta',                         # Theta
                'rho',                           # Rho
                'cash_delta',                    # 现金Delta
                'cash_gamma',                    # 现金Gamma
                'cash_vega',                     # 现金Vega
                'cash_theta',                    # 现金Theta
                'cash_rho',                      # 现金Rho
                'pnl_delta',                     # Delta PnL
                'pnl_gamma',                     # Gamma PnL
                'pnl_vega',                      # Vega PnL
                'pnl_theta',                     # Theta PnL
                'pnl_rho',                       # Rho PnL
                'pnl_total',                     # 总PnL
                'pnl_actual',                    # 实际PnL
                'pnl_unexplained',               # 未解释PnL
                'calculation_timestamp'          # 计算时间戳
            ]
        }

    def validate_data(self, df: pd.DataFrame, data_type: str) -> Tuple[bool, List[str]]:
        """
        验证数据完整性

        Args:
            df: 要验证的数据框
            data_type: 数据类型 ('positions', 'market_data', 'merged')

        Returns:
            (是否通过验证, 错误信息列表)
        """
        errors = []
        required_fields = self.get_required_fields()

        if data_type in required_fields:
            missing_fields = [field for field in required_fields[data_type]
                            if field not in df.columns]
            if missing_fields:
                errors.append(f"Missing required fields: {missing_fields}")

        # 检查数据量
        if len(df) == 0:
            errors.append("Data is empty")

        # 检查关键字段的空值
        if data_type == 'positions':
            null_checks = ['contract_id', 'underlying_code', 'option_type', 'strike_price']
        elif data_type == 'market_data':
            null_checks = ['trade_date', 'underlying_code', 'close_price']
        else:
            null_checks = []

        for field in null_checks:
            if field in df.columns and df[field].isnull().any():
                errors.append(f"Null values found in {field}")

        is_valid = len(errors) == 0
        return is_valid, errors
