"""
期权PnL归因计算模块
计算各种风险因子对期权价格变动的贡献
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
import yaml
from loguru import logger

from .greeks_calculator import PyVollibNumbaGreeksCalculator


class PnLAttributionCalculator:
    """PnL归因计算器"""

    def __init__(self, config_path: str = "config/config.yaml"):
        """
        初始化PnL归因计算器

        Args:
            config_path: 配置文件路径
        """
        self.config = self._load_config(config_path)
        self.greeks_calculator = PyVollibNumbaGreeksCalculator()

        # 获取计算参数
        self.pnl_config = self.config.get('pnl_attribution', {})
        self.price_bump = self.pnl_config.get('price_bump_size', 0.01)
        self.vol_bump = self.pnl_config.get('volatility_bump_size', 0.01)
        self.time_decay_days = self.pnl_config.get('time_decay_days', 1)
        self.include_second_order = self.pnl_config.get('include_second_order', True)
        self.include_cross_terms = self.pnl_config.get('include_cross_terms', True)

        logger.info("PnL Attribution Calculator initialized")

    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"Failed to load config: {e}")
            return self._get_default_config()

    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            'pnl_attribution': {
                'price_bump_size': 0.01,
                'volatility_bump_size': 0.01,
                'time_decay_days': 1,
                'include_second_order': True,
                'include_cross_terms': True
            },
            'calculation': {
                'risk_free_rate': 0.015,
                'trading_days_per_year': 252
            }
        }

    def calculate_pnl_attribution(self, df: pd.DataFrame,
                                price_changes: Optional[Dict[str, float]] = None,
                                vol_changes: Optional[Dict[str, float]] = None,
                                time_change: Optional[float] = None) -> Dict[str, np.ndarray]:
        """
        计算PnL归因

        Args:
            df: 包含期权数据的DataFrame
            price_changes: 标的价格变化字典 {underlying_code: change_ratio}
            vol_changes: 波动率变化字典 {underlying_code: change_ratio}
            time_change: 时间变化（天数）

        Returns:
            包含各项PnL归因的字典
        """
        logger.info("Starting PnL attribution calculation")

        # 如果没有提供变化，从实际数据计算
        if price_changes is None:
            price_changes = self._calculate_actual_price_changes(df)

        if vol_changes is None:
            vol_changes = self._calculate_actual_vol_changes(df)

        if time_change is None:
            time_change = self.time_decay_days

        # 计算基准价格和希腊值
        base_greeks = self._calculate_base_greeks(df)

        # 计算各项PnL归因
        pnl_attribution = {}

        # Delta PnL (一阶价格效应)
        pnl_attribution['delta'] = self._calculate_delta_pnl(df, base_greeks, price_changes)

        # Gamma PnL (二阶价格效应)
        if self.include_second_order:
            pnl_attribution['gamma'] = self._calculate_gamma_pnl(df, base_greeks, price_changes)
        else:
            pnl_attribution['gamma'] = np.zeros(len(df))

        # Vega PnL (波动率效应)
        pnl_attribution['vega'] = self._calculate_vega_pnl(df, base_greeks, vol_changes)

        # Theta PnL (时间衰减效应)
        pnl_attribution['theta'] = self._calculate_theta_pnl(df, base_greeks, time_change)

        # Rho PnL (利率效应)
        pnl_attribution['rho'] = self._calculate_rho_pnl(df, base_greeks)

        # 交叉项 (Cross terms) - 已被移除
        # pnl_attribution['volga'] = np.zeros(len(df))
        # pnl_attribution['vanna'] = np.zeros(len(df))
        # pnl_attribution['charm'] = np.zeros(len(df))

        # 计算总PnL
        pnl_attribution['total'] = self._calculate_total_pnl(pnl_attribution)

        # 计算实际PnL（如果有新价格）
        pnl_attribution['actual'] = self._calculate_actual_pnl(df, base_greeks, price_changes, vol_changes, time_change)

        # 计算解释误差
        pnl_attribution['unexplained'] = pnl_attribution['actual'] - pnl_attribution['total']

        # 添加详细的调试信息
        self._log_pnl_attribution_summary(pnl_attribution)

        logger.info("PnL attribution calculation completed")
        return pnl_attribution

    def _log_pnl_attribution_summary(self, pnl_attribution: Dict[str, np.ndarray]) -> None:
        """记录PnL归因汇总信息用于调试"""
        logger.info("=== PnL Attribution Summary ===")

        components = ['delta', 'gamma', 'vega', 'theta', 'rho']
        total_attributed = 0

        for component in components:
            if component in pnl_attribution:
                component_total = pnl_attribution[component].sum()
                total_attributed += component_total
                logger.info(f"{component.capitalize()} PnL: {component_total:,.2f}")

        actual_total = pnl_attribution['actual'].sum()
        total_calculated = pnl_attribution['total'].sum()
        unexplained = pnl_attribution['unexplained'].sum()

        logger.info(f"Total Attributed PnL: {total_attributed:,.2f}")
        logger.info(f"Total Calculated PnL: {total_calculated:,.2f}")
        logger.info(f"Actual PnL: {actual_total:,.2f}")
        logger.info(f"Unexplained PnL: {unexplained:,.2f}")

        if actual_total != 0:
            explanation_ratio = total_calculated / actual_total
            logger.info(f"Explanation Ratio: {explanation_ratio:.4f} ({explanation_ratio*100:.2f}%)")
        else:
            logger.warning("Actual PnL is zero - cannot calculate explanation ratio")

        logger.info("=== End PnL Attribution Summary ===")

    def _calculate_base_greeks(self, df: pd.DataFrame) -> Dict[str, np.ndarray]:
        """计算基准希腊值"""
        greeks = self.greeks_calculator.calculate_greeks_batch(
            S_arr=df['underlying_price'].values,
            K_arr=df['strike_price'].values,
            T_arr=df['time_to_expiry'].values,
            r_arr=df['risk_free_rate'].values,
            sigma_arr=df['implied_volatility'].values,
            option_type_arr=df['option_type_num'].values
        )

        # 计算现金希腊值
        cash_greeks = self.greeks_calculator.calculate_cash_greeks_batch(
            S_arr=df['underlying_price'].values,
            K_arr=df['strike_price'].values,
            T_arr=df['time_to_expiry'].values,
            r_arr=df['risk_free_rate'].values,
            sigma_arr=df['implied_volatility'].values,
            option_type_arr=df['option_type_num'].values,
            positions_arr=df['position'].values,
            mults_arr=df['contract_multiplier'].values
        )

        # 合并标准希腊值和现金希腊值
        all_greeks = {**greeks, **cash_greeks}
        return all_greeks

    def _calculate_delta_pnl(self, df: pd.DataFrame, base_greeks: Dict,
                           price_changes: Dict[str, float]) -> np.ndarray:
        """
        计算Delta PnL

        根据数学框架：Delta PnL = Δ × ΔS × Q × M
        其中：
        - Δ: Delta值 (期权价格对标的价格的一阶导数)
        - ΔS: 标的价格变动 (绝对值，不是比例)
        - Q: 持仓数量
        - M: 合约乘数
        """
        delta_pnl = np.zeros(len(df))

        for i, row in df.iterrows():
            underlying_code = row['underlying_code']
            if underlying_code in price_changes:
                price_change_ratio = price_changes[underlying_code]
                underlying_price = row['underlying_price']

                # 计算标的价格的绝对变动：ΔS = S × (price_change_ratio)
                delta_s = underlying_price * price_change_ratio

                # Delta PnL = Δ × ΔS × Q × M
                delta_pnl[i] = (base_greeks['delta'][i] * delta_s *
                              row['position'] * row['contract_multiplier'])

        logger.debug(f"Delta PnL calculation: total = {delta_pnl.sum():.2f}")
        return delta_pnl

    def _calculate_gamma_pnl(self, df: pd.DataFrame, base_greeks: Dict,
                           price_changes: Dict[str, float]) -> np.ndarray:
        """
        计算Gamma PnL (二阶效应)

        根据数学框架：Gamma PnL = 0.5 × Γ × (ΔS)² × Q × M
        其中：
        - Γ: Gamma值 (期权价格对标的价格的二阶导数)
        - ΔS: 标的价格变动 (绝对值)
        - Q: 持仓数量
        - M: 合约乘数
        """
        gamma_pnl = np.zeros(len(df))

        for i, row in df.iterrows():
            underlying_code = row['underlying_code']
            if underlying_code in price_changes:
                price_change_ratio = price_changes[underlying_code]
                underlying_price = row['underlying_price']

                # 计算标的价格的绝对变动：ΔS = S × (price_change_ratio)
                delta_s = underlying_price * price_change_ratio

                # Gamma PnL = 0.5 × Γ × (ΔS)² × Q × M
                gamma_pnl[i] = (0.5 * base_greeks['gamma'][i] * (delta_s ** 2) *
                              row['position'] * row['contract_multiplier'])

        logger.debug(f"Gamma PnL calculation: total = {gamma_pnl.sum():.2f}")
        return gamma_pnl

    def _calculate_vega_pnl(self, df: pd.DataFrame, base_greeks: Dict,
                          vol_changes: Dict[str, float]) -> np.ndarray:
        """
        计算Vega PnL

        根据数学框架：Vega PnL = ν × Δσ × Q × M
        其中：
        - ν: Vega值 (期权价格对隐含波动率的导数)
        - Δσ: 隐含波动率变动 (绝对值，如从0.20到0.21，Δσ=0.01)
        - Q: 持仓数量
        - M: 合约乘数

        注意：base_greeks['vega']的单位是期权价格变化/(1%波动率变化)
        """
        vega_pnl = np.zeros(len(df))

        for i, row in df.iterrows():
            underlying_code = row['underlying_code']
            if underlying_code in vol_changes:
                vol_change = vol_changes[underlying_code]

                # Vega PnL = ν × Δσ × Q × M
                # vol_change已经是绝对变化值（如0.01表示1%的波动率变化）
                # base_greeks['vega']的单位是期权价格变化/(1%波动率变化)
                # 所以需要将vol_change转换为百分点：vol_change * 100
                vega_pnl[i] = (base_greeks['vega'][i] * (vol_change * 100) *
                             row['position'] * row['contract_multiplier'])

        logger.debug(f"Vega PnL calculation: total = {vega_pnl.sum():.2f}")
        return vega_pnl

    def _calculate_theta_pnl(self, df: pd.DataFrame, base_greeks: Dict,
                           time_change: float) -> np.ndarray:
        """计算Theta PnL"""
        # Theta PnL = Theta * Δt * Position * Multiplier
        # base_greeks['theta'] 来自 theta_jit, 其单位是期权价格变化 / 天
        # time_change 是经过的天数（例如，1天）
        # Theta PnL = (期权价格变化 / 天) * Δt * Position * Multiplier
        # 这个计算是正确的
        theta_pnl = (base_greeks['theta'] * time_change *
                    df['position'].values * df['contract_multiplier'].values)

        return theta_pnl

    def _calculate_rho_pnl(self, df: pd.DataFrame, base_greeks: Dict,
                         rate_change: float = 0.0) -> np.ndarray:
        """计算Rho PnL"""
        # 通常利率变化很小，这里假设为0
        # Rho PnL = Rho * Δr * Position * Multiplier
        rho_pnl = (base_greeks['rho'] * rate_change *
                  df['position'].values * df['contract_multiplier'].values)

        return rho_pnl

    # 以下交叉项计算函数已被移除
    # def _calculate_cross_terms(self, ...):
    # def _calculate_volga_pnl(self, ...):
    # def _calculate_vanna_pnl(self, ...):
    # def _calculate_charm_pnl(self, ...):

    def _calculate_total_pnl(self, pnl_attribution: Dict[str, np.ndarray]) -> np.ndarray:
        """计算总PnL（各项归因之和，包括交叉项）"""
        total_pnl = np.zeros(len(next(iter(pnl_attribution.values()))))

        # 主要项（一阶和二阶希腊值）
        main_components = ['delta', 'gamma', 'vega', 'theta', 'rho']
        main_total = 0
        
        for component in main_components:
            if component in pnl_attribution:
                component_sum = np.sum(pnl_attribution[component])
                total_pnl += pnl_attribution[component]
                main_total += component_sum
                logger.debug(f"{component.capitalize()} PnL: {component_sum:.2f}")

        # 交叉项（高阶效应） - 已被移除
        cross_total = 0
        # cross_components = ['volga', 'vanna', 'charm'] # 已移除
        # for component in cross_components: # 已移除
        #     if component in pnl_attribution and np.any(pnl_attribution[component]):
        #         ...


        # 记录归因汇总信息
        total_sum = np.sum(total_pnl)
        logger.info(f"PnL归因汇总 - 主要项: {main_total:.2f}, 交叉项: {cross_total:.2f}, 总计: {total_sum:.2f}")
        
        return total_pnl

    def _calculate_actual_pnl(self, df: pd.DataFrame, base_greeks: Dict,
                            price_changes: Dict[str, float], vol_changes: Dict[str, float],
                            time_change: float) -> np.ndarray:
        """
        计算实际PnL（基于实际权利金变动）

        根据数学框架：实际PnL = (Pt - P0) × Q × M
        其中：
        - Pt: 期末期权价格 (option_current_close_price)
        - P0: 期初期权价格 (option_previous_close_price)
        - Q: 持仓数量 (position，买入为正，卖出为负)
        - M: 合约乘数 (contract_multiplier)
        """
        logger.info("Calculating actual PnL using mathematical framework: (Pt - P0) × Q × M")

        # 使用标准的期权PnL计算公式
        price_diff = df['option_current_close_price'] - df['option_previous_close_price']
        actual_pnl = price_diff * df['position'].values * df['contract_multiplier'].values

        logger.info(f"Actual PnL calculation summary:")
        logger.info(f"  - Total actual PnL: {actual_pnl.sum():.2f}")
        logger.info(f"  - Price differences range: [{price_diff.min():.4f}, {price_diff.max():.4f}]")
        logger.info(f"  - Position range: [{df['position'].min()}, {df['position'].max()}]")

        return actual_pnl

    def generate_pnl_summary(self, df: pd.DataFrame, pnl_attribution: Dict[str, np.ndarray]) -> pd.DataFrame:
        """生成PnL归因汇总报告"""
        summary_data = []

        # 按投资组合汇总
        if 'portfolio' in df.columns:
            for portfolio in df['portfolio'].unique():
                mask = df['portfolio'] == portfolio
                portfolio_summary = self._calculate_portfolio_summary(df[mask], pnl_attribution, mask)
                portfolio_summary['portfolio'] = portfolio
                summary_data.append(portfolio_summary)

        # 按标的资产汇总
        for underlying in df['underlying_code'].unique():
            mask = df['underlying_code'] == underlying
            underlying_summary = self._calculate_portfolio_summary(df[mask], pnl_attribution, mask)
            underlying_summary['underlying_code'] = underlying
            summary_data.append(underlying_summary)

        # 总体汇总
        total_mask = np.ones(len(df), dtype=bool)
        total_summary = self._calculate_portfolio_summary(df, pnl_attribution, total_mask)
        total_summary['category'] = 'Total'
        summary_data.append(total_summary)

        return pd.DataFrame(summary_data)

    def _calculate_portfolio_summary(self, df: pd.DataFrame, pnl_attribution: Dict[str, np.ndarray],
                                   mask: np.ndarray) -> Dict:
        """计算投资组合汇总统计"""
        summary = {}

        for component, values in pnl_attribution.items():
            masked_values = values[mask]
            summary[f'{component}_pnl'] = masked_values.sum()
            summary[f'{component}_pnl_abs'] = np.abs(masked_values).sum()

        # 计算解释比例
        if 'total' in pnl_attribution and 'actual' in pnl_attribution:
            total_pnl = pnl_attribution['total'][mask].sum()
            actual_pnl = pnl_attribution['actual'][mask].sum()

            if actual_pnl != 0:
                summary['explanation_ratio'] = total_pnl / actual_pnl
            else:
                summary['explanation_ratio'] = 1.0

        return summary

    def _calculate_actual_price_changes(self, df: pd.DataFrame) -> Dict[str, float]:
        """从实际数据计算价格变化"""
        price_changes = {}
        has_actual_data = False
        
        for underlying_code in df['underlying_code'].unique():
            mask = df['underlying_code'] == underlying_code
            subset = df[mask]
            
            if len(subset) > 0 and 'underlying_current_close_price' in df.columns and 'underlying_previous_close_price' in df.columns:
                # 使用第一条记录的价格数据（假设同一标的的价格变化相同）
                current_price = subset['underlying_current_close_price'].iloc[0]
                previous_price = subset['underlying_previous_close_price'].iloc[0]
                
                if previous_price != 0:
                    price_change = (current_price - previous_price) / previous_price
                    price_changes[underlying_code] = price_change
                    has_actual_data = True
                    logger.info(f"Calculated actual price change for {underlying_code}: {price_change:.4f}")
                else:
                    price_changes[underlying_code] = 0.0
                    logger.warning(f"Previous price is zero for {underlying_code}, using 0 change")
            else:
                # 如果没有实际价格数据，使用默认值
                price_changes[underlying_code] = self.price_bump
                logger.warning(f"No actual price data for {underlying_code}, using default bump: {self.price_bump}")
        
        # 如果没有任何实际价格变动数据，发出提示
        if not has_actual_data:
            logger.warning("⚠️  警告：没有找到实际的价格变动数据，所有标的都使用默认的价格冲击值进行计算")
            print("⚠️  警告：没有找到实际的价格变动数据，PnL归因结果可能不准确")
        
        return price_changes

    def _calculate_actual_vol_changes(self, df: pd.DataFrame) -> Dict[str, float]:
        """
        从实际数据计算隐含波动率变化

        使用更保守和现实的方法来计算波动率变化，避免极端值
        """
        vol_changes = {}
        has_actual_vol_data = False
        underlying_codes = df['underlying_code'].unique()

        # 计算实际波动率变化
        for underlying_code in underlying_codes:
            subset = df[df['underlying_code'] == underlying_code]
            if not subset.empty:
                # 获取当前隐含波动率
                current_iv = subset['implied_volatility'].iloc[0]

                if pd.notna(current_iv) and current_iv > 0 and current_iv < 5.0:  # 限制在合理范围内
                    # 检查是否有前一日价格数据
                    if (pd.notna(subset['option_previous_close_price'].iloc[0]) and
                        pd.notna(subset['underlying_previous_close_price'].iloc[0]) and
                        subset['option_previous_close_price'].iloc[0] > 0):

                        # 获取前一日数据
                        option_prev_price = subset['option_previous_close_price'].iloc[0]
                        underlying_prev_price = subset['underlying_previous_close_price'].iloc[0]
                        strike_price = subset['strike_price'].iloc[0]
                        expiry_date = pd.to_datetime(subset['expiry_date'].iloc[0])
                        option_type = subset['option_type'].iloc[0]

                        # 计算到期时间（前一日）
                        trade_date = pd.to_datetime(subset['trade_date'].iloc[0])
                        prev_date = trade_date - pd.Timedelta(days=1)
                        time_to_expiry_prev = max((expiry_date - prev_date).days / 365.0, 1/365.0)

                        # 使用希腊值计算器计算前一日隐含波动率
                        try:
                            from .greeks_calculator import PyVollibNumbaGreeksCalculator
                            calculator = PyVollibNumbaGreeksCalculator()

                            option_type_val = 1 if option_type.upper() == 'C' else -1
                            prev_iv = calculator.calculate_implied_volatility_batch(
                                S_arr=np.array([underlying_prev_price]),
                                K_arr=np.array([strike_price]),
                                T_arr=np.array([time_to_expiry_prev]),
                                r_arr=np.array([0.015]),  # 假设无风险利率1.5%
                                market_price_arr=np.array([option_prev_price]),
                                option_type_arr=np.array([option_type_val])
                            )[0]

                            # 更严格的合理性检查
                            if (prev_iv > 0.05 and prev_iv < 2.0 and current_iv > 0.05 and current_iv < 2.0):
                                # 计算隐含波动率变化，并限制在合理范围内
                                vol_change = current_iv - prev_iv
                                # 限制单日波动率变化在±20%以内
                                vol_change = max(-0.20, min(0.20, vol_change))
                                vol_changes[underlying_code] = vol_change
                                has_actual_vol_data = True
                                logger.info(f"Calculated actual IV change for {underlying_code}: {vol_change:.4f} (from {prev_iv:.4f} to {current_iv:.4f})")
                            else:
                                # 使用基于价格变化的估算波动率变化
                                price_change = subset['underlying_current_close_price'].iloc[0] / underlying_prev_price - 1
                                # 简单估算：波动率变化约为价格变化的10%
                                estimated_vol_change = price_change * 0.1
                                estimated_vol_change = max(-0.05, min(0.05, estimated_vol_change))  # 限制在±5%
                                vol_changes[underlying_code] = estimated_vol_change
                                logger.info(f"Using estimated IV change for {underlying_code}: {estimated_vol_change:.4f} (based on price change {price_change:.4f})")
                                has_actual_vol_data = True

                        except Exception as e:
                            logger.warning(f"Failed to calculate previous IV for {underlying_code}: {e}")
                            # 使用默认的小幅波动率变化
                            vol_changes[underlying_code] = 0.0  # 假设波动率无变化
                    else:
                        vol_changes[underlying_code] = 0.0  # 假设波动率无变化
                        logger.warning(f"No previous price data for {underlying_code}, assuming no vol change")
                else:
                    vol_changes[underlying_code] = 0.0  # 假设波动率无变化
                    logger.warning(f"Unreasonable current IV for {underlying_code}: {current_iv:.4f}, assuming no vol change")
            else:
                vol_changes[underlying_code] = 0.0
                logger.warning(f"No data for {underlying_code}, assuming no vol change")

        # 如果没有任何实际波动率变动数据，发出提示
        if not has_actual_vol_data:
            logger.warning("⚠️  警告：没有找到实际的隐含波动率变动数据，假设所有标的波动率无变化")
            print("⚠️  警告：没有找到实际的隐含波动率变动数据，Vega PnL归因结果可能不准确")

        return vol_changes
